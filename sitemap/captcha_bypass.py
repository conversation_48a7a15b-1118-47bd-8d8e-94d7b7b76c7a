import time
import random
from typing import <PERSON>ple, Optional

from selenium import webdriver
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException

def create_anti_detection_driver() -> webdriver.Chrome:
    """
    创建一个配置了反检测选项的Chrome WebDriver实例。

    Returns:
        配置好的Chrome WebDriver实例。
    """
    # 配置Chrome选项以避免被检测为自动化工具
    options = webdriver.ChromeOptions()
    options.add_argument("--disable-blink-features=AutomationControlled")
    options.add_experimental_option("excludeSwitches", ["enable-automation"])
    options.add_experimental_option('useAutomationExtension', False)

    # 启动浏览器
    driver = webdriver.Chrome(options=options)

    # 伪装浏览器指纹
    driver.execute_cdp_cmd("Page.addScriptToEvaluateOnNewDocument", {
        "source": """
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined
            })
        """
    })

    return driver

def bypass_nc_slider(
        driver,
        slider_element_id: str = "nc_1_n1z",
        bg_element_id: str = "nc_1__bg",
        max_attempts: int = 5,
        delay_range: Tuple[float, float] = (0.2, 0.5),
        start_position: float = 24,
        end_position: float = 240,
        retry_button_xpath: Optional[str] = "//span[contains(text(), '重试') or contains(text(), '刷新')]"
) -> bool:
    """
    绕过阿里巴巴/网易滑块验证码，通过模拟人类行为和多次尝试提高成功率。

    Args:
        driver: Selenium WebDriver 实例。
        slider_element_id: 滑块按钮元素的 ID (默认: "nc_1_n1z")。
        bg_element_id: 背景元素的 ID (默认: "nc_1__bg")。
        max_attempts: 尝试解决滑块的最大次数 (默认: 5)。
        delay_range: 一个元组，指定随机延迟的范围（最小，最大）（以秒为单位），以模拟人类行为 (默认: (0.2, 0.5))。
        start_position: 滑块起始位置的宽度值 (默认: 24)。
        end_position: 滑块目标结束位置的宽度值 (默认: 240)。
        retry_button_xpath: 重试按钮的XPath表达式，如果检测到会自动点击 (默认: 查找包含'重试'或'刷新'文本的span元素)。

    Returns:
        如果成功绕过滑块，则返回 True，否则返回 False。
    """
    for attempt in range(max_attempts):
        try:
            # 等待滑块元素出现
            slider = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.ID, slider_element_id))
            )
            print(f"尝试 {attempt+1}/{max_attempts}: 找到滑块元素")

            # 等待背景元素出现
            background = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.ID, bg_element_id))
            )
            print(f"尝试 {attempt+1}/{max_attempts}: 找到背景元素")

            # 计算目标偏移量（将滑块向右移动此量）
            # 滑块从初始位置滑动到指定结束位置
            offset = end_position - start_position  # 从初始位置滑动到结束位置
            print(f"尝试 {attempt+1}/{max_attempts}: 计算偏移量: 从初始位置{start_position}px滑动到结束位置{end_position}px, 目标偏移={offset}")

            # 模拟人类拖动行为：不是直线移动，而是分步骤移动
            action = ActionChains(driver)
            action.click_and_hold(slider).perform()
            print(f"尝试 {attempt+1}/{max_attempts}: 已点击并按住滑块")

            # 将总偏移量分成多个小步骤，模拟人类的不均匀移动
            remaining_offset = offset
            steps = random.randint(8, 15)  # 更多步数以增加自然感
            current_x = 0
            # 模拟人类移动：开始慢，中间快，结束慢
            for i in range(steps):
                if remaining_offset <= 0:
                    break
                # 根据当前步骤调整移动速度
                if i < steps // 3:  # 开始阶段：慢
                    step_factor = random.uniform(0.2, 0.4)
                elif i < 2 * steps // 3:  # 中间阶段：快
                    step_factor = random.uniform(0.6, 0.9)
                else:  # 结束阶段：慢
                    step_factor = random.uniform(0.1, 0.3)
                step_size = (remaining_offset / (steps - i)) * step_factor
                move = min(remaining_offset, step_size)
                current_x += move
                # 添加微小的垂直偏移，模拟人类手部抖动
                y_offset = random.uniform(-3, 3)
                action.move_by_offset(move, y_offset).perform()
                remaining_offset -= move
                print(f"尝试 {attempt+1}/{max_attempts}: 步骤 {i+1}/{steps}, 移动步长={move:.2f}, 垂直偏移={y_offset:.2f}, 当前X位置={current_x:.2f}, 剩余偏移={remaining_offset:.2f}")
                # 随机小延迟，模拟人类反应时间，结束时延迟更长
                delay = random.uniform(0.03, 0.08) if i < 2 * steps // 3 else random.uniform(0.1, 0.2)
                time.sleep(delay)

            # 释放鼠标按钮
            time.sleep(random.uniform(0.3, 0.6))
            action.release().perform()
            print(f"尝试 {attempt+1}/{max_attempts}: 已释放滑块")

            # 检查成功或失败
            try:
                WebDriverWait(driver, 5).until(EC.staleness_of(slider))
                print(f"尝试 {attempt+1}/{max_attempts}: 成功绕过滑块 (滑块已消失)。")
                return True
            except TimeoutException:
                print(f"尝试 {attempt+1}/{max_attempts}: 滑块可能失败（等待后滑块仍然存在）。")
                # 检查是否有重试按钮或提示
                if retry_button_xpath:
                    try:
                        retry_elements = driver.find_elements(By.XPATH, retry_button_xpath)
                        if retry_elements:
                            retry_elements[0].click()
                            print(f"尝试 {attempt+1}/{max_attempts}: 检测到重试提示，已点击重试。")
                            time.sleep(random.uniform(1, 2))
                    except WebDriverException as e:
                        print(f"尝试 {attempt+1}/{max_attempts}: 点击重试按钮时出错: {e}")

        except TimeoutException as e:
            print(f"尝试 {attempt+1}/{max_attempts}: 找不到滑块或背景元素: {e}")
            return False
        except WebDriverException as e:
            print(f"尝试 {attempt+1}/{max_attempts}: 绕过滑块时出错: {e}")
            time.sleep(random.uniform(1, 2))

        # 每次失败后增加延迟，模拟人类遇到问题后的等待
        retry_delay = random.uniform(delay_range[0] * (attempt + 1), delay_range[1] * (attempt + 1))
        print(f"尝试 {attempt+1}/{max_attempts}: 下一次重试前等待 {retry_delay:.2f} 秒")
        time.sleep(retry_delay)

    print("多次尝试后未能绕过滑块。")
    return False

def solve_captcha_on_page(url: str, slider_id: str = "nc_1_n1z", background_id: str = "nc_1__bg", **kwargs) -> bool:
    """
    在指定URL页面上解决滑块验证码。

    Args:
        url: 包含滑块验证码的页面URL。
        slider_id: 滑块元素的ID。
        background_id: 背景元素的ID。
        **kwargs: 传递给bypass_nc_slider函数的其他参数。

    Returns:
        如果成功绕过滑块，则返回 True，否则返回 False。
    """
    driver = create_anti_detection_driver()
    try:
        print(f"正在打开页面: {url}")
        driver.get(url)
        print("页面加载完成，尝试解决滑块验证码")
        success = bypass_nc_slider(
            driver,
            slider_element_id=slider_id,
            bg_element_id=background_id,
            **kwargs
        )
        if success:
            print("成功绕过滑块！")
            time.sleep(5)  # 等待一段时间以便观察结果
        else:
            print("绕过滑块失败。")
            time.sleep(5)
        return success
    except Exception as e:
        print(f"运行过程中发生错误: {e}")
        return False
    finally:
        driver.quit()

if __name__ == '__main__':
    # 示例用法
    solve_captcha_on_page(
        url="https://www.baidu.com",
        slider_id="nc_1_n1z",
        background_id="nc_1__bg",
        start_position=24,
        end_position=240
    )