import os
import sys
import pickle
import random
from urllib.parse import urljoin, urlparse, urlunparse
from datetime import datetime, timezone
from concurrent.futures import ThreadPoolExecutor, as_completed
from pybloom_live import BloomFilter
import requests
from bs4 import BeautifulSoup
import xml.etree.ElementTree as ET
import xml.dom.minidom
import time
import threading
import importlib.util
from typing import List, Tuple, Optional, IO, Dict, Set, Union

# 尝试导入selenium相关模块
SELENIUM_AVAILABLE = False
try:
    from selenium import webdriver
    from selenium.webdriver.chrome.options import Options as ChromeOptions
    from selenium.webdriver.chrome.service import Service as ChromeService
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.common.exceptions import TimeoutException, WebDriverException

    SELENIUM_AVAILABLE = True
except ImportError:
    pass

# 尝试导入tqdm，如果不存在则使用简单进度指示
try:
    from tqdm import tqdm

    TQDM_AVAILABLE = True
except ImportError:
    TQDM_AVAILABLE = False


    # 简单的进度条替代
    class SimpleTqdm:
        def __init__(self, total=0, desc="", unit=""):
            self.total = total
            self.desc = desc
            self.unit = unit
            self.n = 0
            self.last_print = 0
            print(f"{desc}: 0/{total} {unit}")

        def update(self, n=1):
            self.n += n
            # 只有当进度有明显变化时才打印
            if time.time() - self.last_print > 0.5:
                print(f"\r{self.desc}: {self.n}/{self.total} {self.unit}", end="")
                self.last_print = time.time()

        def total(self, total):
            self.total = total

        def refresh(self):
            print(f"\r{self.desc}: {self.n}/{self.total} {self.unit}", end="")
            self.last_print = time.time()

        def close(self):
            print()  # 换行


    tqdm = SimpleTqdm

# 配置参数
MAX_DEPTH: int = 3
REQUEST_DELAY: float = 0.5
MAX_THREADS: int = 5  # 降低线程数以适应Selenium
BLOOM_CAPACITY: int = 100000
BLOOM_ERROR_RATE: float = 0.001
MAX_RETRIES: int = 3  # 最大重试次数
USE_SELENIUM: bool = True  # 是否使用Selenium
SELENIUM_TIMEOUT: int = 30  # Selenium等待超时（秒）
PAGE_LOAD_TIMEOUT: int = 30  # 页面加载超时（秒）
DRIVER_TYPE: str = "chrome"  # 浏览器类型
COOKIE_DIR: str = "cookies"  # Cookie存储目录
WAIT_AFTER_LOAD: float = 2.0  # 页面加载后等待时间，可以让JavaScript完全执行
VERIFICATION_WAIT: int = 60  # 手动验证等待时间(秒)
RANDOMIZE_DELAY: bool = True  # 随机化请求延迟
MANUAL_VERIFICATION: bool = True  # 是否使用手动验证
HEADLESS_MODE: bool = False  # 是否使用无头模式 (手动验证时应设为False)
USER_AGENTS: List[str] = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Safari/605.1.15",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0"
]


def check_dependencies():
    """检查依赖库是否安装"""
    required_libraries: List[str] = ['requests', 'beautifulsoup4', 'pybloom_live']

    # 检查selenium
    if USE_SELENIUM and not SELENIUM_AVAILABLE:
        required_libraries.append('selenium')

    missing_libraries: List[str] = [lib for lib in required_libraries if not is_library_installed(lib)]
    if missing_libraries:
        install_command = f"pip install {' '.join(missing_libraries)}"
        missing_display = [('bs4' if lib == 'beautifulsoup4' else lib) for lib in missing_libraries]
        raise ImportError(f"缺少依赖库: {', '.join(missing_display)}。请运行: {install_command}")

    # 如果启用 Selenium，检查浏览器驱动程序
    if USE_SELENIUM and SELENIUM_AVAILABLE:
        check_webdriver()


def check_webdriver():
    """检查webdriver是否可用"""
    if DRIVER_TYPE == "chrome":
        try:
            # 尝试创建一个测试 driver
            options = get_chrome_options()
            driver = webdriver.Chrome(options=options)
            driver.quit()
            print("Chrome WebDriver 可用。")
        except WebDriverException as e:
            print(f"警告: Chrome WebDriver 不可用或有问题: {e}")
            print("请确保已安装 Chrome 浏览器和 ChromeDriver，并且 ChromeDriver 与浏览器版本匹配")
            print("下载 ChromeDriver: https://chromedriver.chromium.org/downloads")
            print("放置ChromeDriver到PATH环境变量包含的目录中")
            raise SystemExit(1)


def get_chrome_options(headless=HEADLESS_MODE):
    """配置Chrome浏览器选项"""
    options = ChromeOptions()

    # 更好地模拟真实浏览器，少设一些参数反而更不像机器人
    options.add_argument("--disable-blink-features=AutomationControlled")  # 隐藏自动化控制特征
    options.add_experimental_option("excludeSwitches", ["enable-automation"])  # 不显示"Chrome正在被自动化软件控制"的提示
    options.add_experimental_option("useAutomationExtension", False)  # 不使用自动化扩展

    # 只在需要时使用无头模式
    if headless:
        options.add_argument("--headless=new")  # 新版无头模式，改进了兼容性

    # 基本配置
    options.add_argument("--disable-gpu")
    options.add_argument("--no-sandbox")
    options.add_argument("--disable-dev-shm-usage")

    # 随机化窗口大小，避免总是相同的尺寸
    width = random.randint(1200, 1600)
    height = random.randint(800, 1000)
    options.add_argument(f"--window-size={width},{height}")

    # 随机化用户代理
    options.add_argument(f"--user-agent={get_random_user_agent()}")

    # 语言设置
    options.add_argument("--lang=zh-CN,zh,zh-TW,en-US,en")

    # 禁止加载图片，提高性能 (但如果是手动验证模式，启用图片可能有助于用户识别验证码)
    if headless:
        prefs = {
            "profile.managed_default_content_settings.images": 2,
            "profile.default_content_setting_values.notifications": 2
        }
        options.add_experimental_option("prefs", prefs)

    return options


def create_webdriver(headless=HEADLESS_MODE):
    """创建并返回配置好的WebDriver实例"""
    try:
        from captcha_bypass import create_anti_detection_driver
        driver = create_anti_detection_driver()
        print("使用captcha_bypass创建了反检测WebDriver")

        # 设置超时
        driver.set_page_load_timeout(PAGE_LOAD_TIMEOUT)
        driver.set_script_timeout(PAGE_LOAD_TIMEOUT)
        return driver
    except ImportError:
        print("警告：captcha_bypass模块不可用，将使用默认配置创建WebDriver")
        if DRIVER_TYPE == "chrome":
            options = get_chrome_options(headless)
            driver = webdriver.Chrome(options=options)

            # 设置超时
            driver.set_page_load_timeout(PAGE_LOAD_TIMEOUT)
            driver.set_script_timeout(PAGE_LOAD_TIMEOUT)

            # 执行stealth js脚本使Chrome更难被检测为自动化
            stealth_js = """
            Object.defineProperty(navigator, 'webdriver', {
                get: () => false,
            });
            window.navigator.chrome = {
                runtime: {},
            };
            Object.defineProperty(navigator, 'languages', {
                get: () => ['zh-CN', 'zh', 'en'],
            });
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5],
            });
            """
            try:
                driver.execute_script(stealth_js)
            except:
                pass

            return driver
        else:
            raise ValueError(f"不支持的浏览器类型: {DRIVER_TYPE}")


def is_library_installed(library_name: str) -> bool:
    """检查指定库是否已安装"""
    if library_name == 'beautifulsoup4':
        library_name = 'bs4'
    return importlib.util.find_spec(library_name) is not None


def get_random_user_agent() -> str:
    """返回一个随机的用户代理字符串"""
    import random
    return random.choice(USER_AGENTS)


def extract_internal_links(domain: str, output_file_handle: IO[str], lock: threading.Lock):
    """提取指定域名下的所有内部链接并直接写入文件"""
    print(f"开始爬取域名: {domain}")
    print(f"使用 {'Selenium' if USE_SELENIUM and SELENIUM_AVAILABLE else 'Requests'} 获取页面内容")
    if MANUAL_VERIFICATION:
        print("已启用手动验证模式，遇到验证页面时将弹出浏览器窗口")

    # 确保cookie目录存在
    cookie_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), COOKIE_DIR)
    if not os.path.exists(cookie_dir):
        os.makedirs(cookie_dir)

    # 获取domain对应的cookie文件路径
    domain_netloc = urlparse(domain).netloc
    cookie_file = os.path.join(cookie_dir, f"{domain_netloc.replace('.', '_')}.cookies")

    # 计数器和状态信息
    stats = {
        'total_processed': 0,
        'total_errors': 0,
        'total_links_written': 0
    }
    stats_lock = threading.Lock()

    # 创建爬取集和队列
    crawled: BloomFilter = BloomFilter(capacity=BLOOM_CAPACITY, error_rate=BLOOM_ERROR_RATE)
    to_crawl: List[Tuple[str, int]] = [(normalize_url(domain), 0)]
    crawled.add(normalize_url(domain))

    # 创建进度显示
    pbar = tqdm(total=1, desc="爬取进度", unit="URL")

    # 如果使用Selenium，创建WebDriver池
    driver_pool = []
    driver_pool_lock = threading.Lock()

    if USE_SELENIUM and SELENIUM_AVAILABLE:
        # 根据线程数预先创建WebDriver实例
        try:
            print(f"正在初始化 {min(MAX_THREADS, 3)} 个WebDriver实例...")
            for _ in range(min(MAX_THREADS, 3)):  # 限制初始驱动数量
                driver = create_webdriver()  # 使用默认HEADLESS_MODE
                driver_pool.append(driver)
            print(f"已创建 {len(driver_pool)} 个WebDriver实例")
        except Exception as e:
            print(f"初始化WebDriver时出错: {e}")
            print("将回退到使用requests库")

    def get_driver_from_pool(headless=HEADLESS_MODE):
        """从池中获取一个WebDriver，如果没有可用的则创建一个新的"""
        with driver_pool_lock:
            if driver_pool and headless == HEADLESS_MODE:  # 只有当请求的headless模式与池中的一致时才复用
                return driver_pool.pop()
            else:
                try:
                    return create_webdriver(headless)
                except Exception as e:
                    print(f"创建新WebDriver实例时出错: {e}")
                    return None

    def return_driver_to_pool(driver, headless=HEADLESS_MODE):
        """将WebDriver返回到池中，只有与池配置匹配的才返回"""
        if driver:
            try:
                # 检查是否为无头模式（如果支持的话）
                is_headless = True
                try:
                    # 尝试通过执行JS检测是否为无头模式
                    is_headless = driver.execute_script(
                        "return navigator.userAgent.indexOf('Headless') !== -1 || "
                        "window.outerWidth === 0 || window.outerHeight === 0;"
                    )
                except:
                    # 如果无法确定，假设与默认HEADLESS_MODE相同
                    is_headless = HEADLESS_MODE

                if is_headless == HEADLESS_MODE:  # 只有当driver的headless模式与池中的一致时才返回池
                    driver.delete_all_cookies()  # 清除所有cookie
                    with driver_pool_lock:
                        driver_pool.append(driver)
                else:
                    # 如果与池配置不匹配，则关闭而不返回池
                    driver.quit()
            except Exception as e:
                print(f"归还WebDriver到池时出错: {e}")
                try:
                    driver.quit()
                except:
                    pass

    def update_stats(key: str, increment: int = 1):
        """更新统计信息"""
        nonlocal stats
        with stats_lock:
            stats[key] += increment

    def write_url_entry(url: str, domain: str, file_handle: IO[str], writer_lock: threading.Lock):
        """将单个URL条目写入XML文件"""
        try:
            path = url
            url_node = ET.Element("url")
            ET.SubElement(url_node, "loc").text = path

            lastmod = ET.SubElement(url_node, "lastmod")
            lastmod.text = datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%S+00:00")

            priority = ET.SubElement(url_node, "priority")
            priority.text = str(calculate_priority(urlparse(path).path))

            # 使用minidom格式化XML
            xml_string = ET.tostring(url_node, encoding="unicode")
            dom = xml.dom.minidom.parseString(xml_string)
            pretty_xml = dom.toprettyxml(indent="  ")

            # 处理格式化XML
            lines = [line for line in pretty_xml.splitlines() if line.strip() and not line.strip().startswith('<?xml')]
            formatted_xml_entry = "\n".join(["  " + line for line in lines])

            with writer_lock:
                file_handle.write(formatted_xml_entry + "\n")
                update_stats('total_links_written')

        except Exception as e:
            print(f"写入URL {url} 到文件时出错: {e}")

    # 写入初始域名到Sitemap
    write_url_entry(normalize_url(domain), domain, output_file_handle, lock)

    def fetch_with_selenium(url: str) -> Tuple[str, List[str]]:
        """使用Selenium获取页面内容和链接，包含绕过验证逻辑"""
        driver = None
        # 默认使用配置的headless模式
        headless = HEADLESS_MODE

        try:
            # 先使用标准模式获取driver
            driver = get_driver_from_pool(headless)
            if not driver:
                raise WebDriverException("无法获取WebDriver实例")

            # 尝试加载之前保存的cookies
            domain_netloc = urlparse(url).netloc
            cookie_file = os.path.join(cookie_dir, f"{domain_netloc.replace('.', '_')}.cookies")

            if os.path.exists(cookie_file):
                # 先访问域名根路径，然后加载cookies
                root_url = f"{urlparse(url).scheme}://{urlparse(url).netloc}/"
                driver.get(root_url)

                try:
                    cookies = pickle.load(open(cookie_file, "rb"))
                    for cookie in cookies:
                        # 有些cookie可能有domain限制，跳过设置过程中的错误
                        try:
                            driver.add_cookie(cookie)
                        except:
                            pass
                    print(f"已加载保存的cookies: {cookie_file}")
                except Exception as e:
                    print(f"读取cookies时出错: {e}")

            # 访问URL
            print(f"Selenium访问: {url}")
            driver.get(url)

            # 检测是否需要绕过验证页面 - 基于几种常见的验证页面特征
            is_verification_page = False
            verification_checks = [
                lambda d: "验证" in d.title.lower() or "verify" in d.title.lower() or "check" in d.title.lower(),
                lambda d: "cloudflare" in d.page_source.lower() and (
                            "检查" in d.page_source.lower() or "checking" in d.page_source.lower()),
                lambda d: d.current_url != url and (
                            "captcha" in d.current_url.lower() or "challenge" in d.current_url.lower()),
                lambda d: len(
                    d.find_elements(By.XPATH, "//iframe[contains(@src, 'captcha') or contains(@src, 'challenge')]")) > 0
            ]

            for check in verification_checks:
                try:
                    if check(driver):
                        is_verification_page = True
                        break
                except:
                    continue

            if is_verification_page:
                # 检测到验证页面
                print("检测到访问验证页面，尝试自动绕过验证...")
                # 首先尝试使用 captcha_bypass 自动绕过
                if attempt_to_bypass_captcha(driver, url):
                    print("成功自动绕过验证！继续爬取过程...")
                    # 页面可能已更新，继续执行
                # 如果自动绕过失败，再尝试手动验证
                elif MANUAL_VERIFICATION:
                    # 如果当前是无头模式，需要关闭并重新创建有窗口的浏览器
                    if headless:
                        print("检测到验证页面，切换到有窗口的浏览器进行手动验证...")

                        # 保存当前URL
                        current_url = driver.current_url

                        # 关闭无头浏览器
                        if driver:
                            driver.quit()
                            driver = None

                        # 创建有窗口的浏览器
                        driver = create_webdriver(headless=False)

                        # 重新加载URL
                        driver.get(current_url)

                    # 提示用户和设置窗口大小和位置
                    driver.set_window_size(1000, 800)  # 设置合适的窗口大小
                    try:
                        driver.set_window_position(50, 50)  # 将窗口放在屏幕的可见区域
                    except:
                        pass

                    # 警告信息
                    print("\n" + "=" * 80)
                    print("检测到访问验证页面！浏览器窗口已打开，请完成验证。")
                    print(f"您有 {VERIFICATION_WAIT} 秒的时间完成验证。")
                    print("验证完成后，浏览器将自动继续执行。")
                    print("=" * 80 + "\n")

                    # 在控制台发出声音提醒
                    print('\a')  # 发出系统提示音

                    # 等待用户完成验证
                    wait_start = time.time()
                    verification_complete = False
                    last_url = driver.current_url

                    while time.time() - wait_start < VERIFICATION_WAIT:
                        time.sleep(1)
                        # 周期性地发出声音提醒
                        if int(time.time() - wait_start) % 10 == 0:
                            print('\a')
                            remain_time = VERIFICATION_WAIT - int(time.time() - wait_start)
                            if remain_time > 0:
                                print(f"请在 {remain_time} 秒内完成验证...")

                        # 检查是否仍在验证页面
                        still_verification = False
                        for check in verification_checks:
                            try:
                                if check(driver):
                                    still_verification = True
                                    break
                            except:
                                continue

                        if not still_verification:
                            print("验证已完成！继续爬取过程...")
                            verification_complete = True
                            break

                    if not verification_complete:
                        print("验证超时！尝试继续处理...")

                # 保存cookies以便后续使用
                try:
                    cookies = driver.get_cookies()
                    pickle.dump(cookies, open(cookie_file, "wb"))
                    print(f"已保存cookies到: {cookie_file}")
                except Exception as e:
                    print(f"保存cookies时出错: {e}")

            # 等待页面完全加载
            try:
                WebDriverWait(driver, SELENIUM_TIMEOUT).until(
                    EC.presence_of_element_located((By.TAG_NAME, "body"))
                )
            except:
                print("等待body元素超时，但继续处理")

            # 额外等待，确保JavaScript完成执行
            time.sleep(WAIT_AFTER_LOAD)

            # 获取页面源码
            page_source = driver.page_source

            # 查找所有链接
            hrefs = []
            elements = driver.find_elements(By.XPATH, "//a[@href] | //link[@href]")
            for element in elements:
                href = element.get_attribute("href")
                if href:
                    hrefs.append(href)

            return page_source, hrefs
        except TimeoutException:
            print(f"Selenium访问 {url} 超时")
            raise
        except WebDriverException as e:
            print(f"Selenium访问 {url} 出错: {e}")
            raise
        finally:
            if driver:
                return_driver_to_pool(driver, headless)

    def attempt_to_bypass_captcha(driver, url: str) -> bool:
        """尝试绕过页面上的滑块验证码"""
        try:
            from captcha_bypass import bypass_nc_slider
            print(f"检测到可能需要验证码，尝试绕过滑块验证: {url}")

            # 尝试查找常见的滑块元素
            slider_ids = ["nc_1_n1z", "nc-1-n1z", "nc_1__bg", "verify-slider", "slider-btn"]
            bg_ids = ["nc_1__bg", "nc-1-bg", "verify-bg", "slider-bg"]

            slider_id = None
            bg_id = None

            # 尝试查找滑块元素
            for sid in slider_ids:
                try:
                    if driver.find_element(By.ID, sid).is_displayed():
                        slider_id = sid
                        print(f"找到滑块元素ID: {slider_id}")
                        break
                except:
                    continue

            # 尝试查找背景元素
            for bid in bg_ids:
                try:
                    if driver.find_element(By.ID, bid).is_displayed():
                        bg_id = bid
                        print(f"找到背景元素ID: {bg_id}")
                        break
                except:
                    continue

            # 如果找不到特定ID，尝试使用XPath查找滑块元素
            if not slider_id:
                try:
                    xpath_patterns = [
                        "//div[contains(@class, 'slider')]",
                        "//div[contains(@class, 'captcha')]//span",
                        "//div[contains(@id, 'nc_')]//span",
                        "//div[contains(@class, 'verify')]//span"
                    ]
                    for xpath in xpath_patterns:
                        elements = driver.find_elements(By.XPATH, xpath)
                        if elements:
                            print(f"找到可能的滑块元素，使用XPath: {xpath}")
                            # 使用默认值
                            slider_id = "nc_1_n1z"
                            bg_id = "nc_1__bg"
                            break
                except:
                    pass

            # 如果找到了滑块元素，尝试绕过
            if slider_id:
                success = bypass_nc_slider(
                    driver,
                    slider_element_id=slider_id,
                    bg_element_id=bg_id or "nc_1__bg",  # 如果找不到背景ID，使用默认值
                    start_position=24,
                    end_position=240,
                    max_attempts=5
                )
                if success:
                    print("成功绕过滑块验证码！")
                else:
                    print("未能绕过滑块验证码。")
                return success
            else:
                print("未找到滑块验证码元素。")
                return False
        except ImportError:
            print("警告：captcha_bypass模块不可用，无法自动绕过验证码")
            return False
        except Exception as e:
            print(f"尝试绕过验证码时发生错误: {e}")
            return False

    def fetch_url(url: str, depth: int, base_domain: str) -> List[Tuple[str, int]]:
        """抓取URL并返回新的内部链接，包含重试机制"""
        if depth > MAX_DEPTH:
            return []

        newly_found_links: List[Tuple[str, int]] = []

        # 重试机制
        for attempt in range(MAX_RETRIES):
            try:
                # 添加随机延迟，避免太规律
                if RANDOMIZE_DELAY:
                    delay = REQUEST_DELAY * (1 + random.random() + 0.5 * attempt)
                else:
                    delay = REQUEST_DELAY * (1 + 0.5 * attempt)
                time.sleep(delay)

                # 获取基础域名
                base_domain_netloc = urlparse(base_domain).netloc

                # 使用Selenium或传统方法获取页面内容
                if USE_SELENIUM and SELENIUM_AVAILABLE and driver_pool:
                    try:
                        _, all_hrefs = fetch_with_selenium(url)
                    except (TimeoutException, WebDriverException):
                        if attempt == MAX_RETRIES - 1:  # 最后一次尝试使用传统方法
                            print(f"Selenium失败，尝试使用Requests作为后备 ({url})")
                            # 回退到传统方法
                            response = requests.get(url, timeout=30, headers={'User-Agent': get_random_user_agent()})
                            response.raise_for_status()
                            soup = BeautifulSoup(response.content, 'html.parser')
                            all_hrefs = []
                            for tag in soup.find_all(['a', 'link'], href=True):
                                href = tag.get('href')
                                if href:
                                    all_hrefs.append(href)
                        else:
                            continue  # 再次尝试使用Selenium
                else:
                    # 传统方法
                    response = requests.get(url, timeout=30, headers={'User-Agent': get_random_user_agent()})
                    response.raise_for_status()
                    soup = BeautifulSoup(response.content, 'html.parser')
                    all_hrefs = []
                    for tag in soup.find_all(['a', 'link'], href=True):
                        href = tag.get('href')
                        if href:
                            all_hrefs.append(href)

                # 处理找到的链接
                for href in all_hrefs:
                    if not href:
                        continue

                    # 处理相对链接
                    full_url = urljoin(url, href).split('#')[0]
                    parsed_full = urlparse(full_url)

                    # 检查协议
                    if parsed_full.scheme not in ('http', 'https'):
                        continue

                    # 检查是否为内部链接
                    if parsed_full.netloc != base_domain_netloc:
                        continue

                    # 标准化URL
                    normalized = normalize_url(full_url)

                    # 添加到爬取队列
                    with lock:
                        if normalized not in crawled:
                            crawled.add(normalized)
                            newly_found_links.append((normalized, depth + 1))

                # 更新统计和进度
                with stats_lock:
                    stats['total_processed'] += 1
                pbar.update(1)

                if newly_found_links:
                    # 更新进度条总数
                    pbar.total += len(newly_found_links)
                    pbar.refresh()

                # 请求成功，跳出重试循环
                break

            except Exception as e:
                if attempt < MAX_RETRIES - 1:
                    print(f"抓取 {url} 时出错 (尝试 {attempt + 1}/{MAX_RETRIES}): {e}")
                    continue
                else:
                    update_stats('total_errors')
                    print(f"抓取 {url} 时出错，已达到最大重试次数: {e}")
                    break

        return newly_found_links

    try:
        with ThreadPoolExecutor(max_workers=MAX_THREADS) as executor:
            # 初始化future字典
            futures_map = {executor.submit(fetch_url, url, depth, domain): url for url, depth in to_crawl}
            to_crawl.clear()

            processed_urls: Set[str] = set()  # 跟踪已处理的URL

            # 处理队列
            while futures_map:
                # 等待完成的任务，超时时间较长以适应慢速网站
                current_batch = list(futures_map.keys())
                for future in as_completed(current_batch, timeout=180):
                    original_url = futures_map.pop(future)
                    processed_urls.add(original_url)  # 标记为已处理

                    try:
                        # 处理结果
                        new_links = future.result()

                        # 收集新任务
                        pending_tasks = []
                        for new_url, next_depth in new_links:
                            # 检查URL是否已经被处理过或正在处理中
                            if (new_url not in processed_urls and
                                    new_url not in [task['url'] for task in pending_tasks] and
                                    new_url not in futures_map.values()):

                                # 将URL写入sitemap
                                write_url_entry(new_url, domain, output_file_handle, lock)

                                # 如果深度允许，添加到待爬取队列
                                if next_depth <= MAX_DEPTH:
                                    pending_tasks.append({'url': new_url, 'depth': next_depth})

                        # 提交新任务
                        for task in pending_tasks:
                            new_future = executor.submit(fetch_url, task['url'], task['depth'], domain)
                            futures_map[new_future] = task['url']

                    except Exception as exc:
                        update_stats('total_errors')
                        print(f"{original_url} 处理时发生异常: {exc}")

                # 如果当前批次已处理完，但仍有futures，检查是否有超时
                if not current_batch and futures_map:
                    print(f"等待中的任务: {len(futures_map)}，URL: {list(futures_map.values())[:5]}")
    finally:
        # 关闭进度条
        pbar.close()

        # 清理所有WebDriver实例
        if USE_SELENIUM and SELENIUM_AVAILABLE:
            print("正在清理WebDriver资源...")
            for driver in driver_pool:
                try:
                    driver.quit()
                except:
                    pass

    # 输出统计信息
    print(f"\n爬取完成！")
    print(f"总计处理URL: {stats['total_processed']}")
    print(f"成功写入URL: {stats['total_links_written']}")
    print(f"错误数: {stats['total_errors']}")


def normalize_url(url: str) -> str:
    """URL标准化处理，确保相同的URL有相同的表示形式"""
    try:
        parsed = urlparse(url)

        # 处理协议
        scheme = parsed.scheme.lower()

        # 处理域名
        netloc = parsed.netloc.lower()

        # 处理路径
        path = parsed.path
        if not path:
            path = '/'
        # 移除末尾的斜杠，除非是根路径
        if path != '/' and path.endswith('/'):
            path = path.rstrip('/')

        # 处理查询参数（可能需要排序）
        query = parsed.query

        # 重组URL
        normalized = urlunparse((
            scheme,
            netloc,
            path,
            parsed.params,
            query,
            ''  # 移除片段标识符
        ))

        return normalized
    except Exception as e:
        print(f"标准化URL时出错: {url}, 错误: {e}")
        return url


def calculate_priority(url_path: str) -> float:
    """根据URL路径深度计算优先级"""
    # 计算路径深度
    depth = len([part for part in url_path.strip('/').split('/') if part])

    # 根路径优先级最高
    if depth == 0:
        return 1.0

    # 根据深度降低优先级
    priority = max(1.0 - depth * 0.1, 0.1)
    return round(priority, 1)


def generate_sitemap(domain: str, output_file: Optional[str] = None, max_depth: Optional[int] = None):
    """生成指定域名的Sitemap XML文件"""
    global MAX_DEPTH

    # 如果提供了自定义深度，更新全局设置
    if max_depth is not None:
        MAX_DEPTH = max_depth

    # 验证域名格式
    if not domain.startswith(('http://', 'https://')):
        raise ValueError("域名必须以 http:// 或 https:// 开头")

    # 确定输出文件名
    if output_file is None:
        script_dir = os.path.dirname(os.path.abspath(__file__))
        domain_part = urlparse(domain).netloc.replace('.', '_')
        timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
        output_file = os.path.join(script_dir, f"sitemap_{domain_part}_{timestamp}.xml")

    # 开始计时
    start_time = time.time()
    print(f"开始生成 Sitemap: {domain}")
    print(f"输出文件: {os.path.abspath(output_file)}")
    print(f"最大爬取深度: {MAX_DEPTH}")

    # 创建文件写入锁
    file_lock = threading.Lock()

    try:
        # 写入XML头部
        with open(output_file, "w", encoding="utf-8") as f:
            f.write('<?xml version="1.0" encoding="UTF-8"?>\n')
            f.write('<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"\n')
            f.write('        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"\n')
            f.write(
                '        xsi:schemaLocation="http://www.sitemaps.org/schemas/sitemap/0.9 http://www.sitemaps.org/schemas/sitemap/0.9/sitemap.xsd">\n')

        # 追加模式打开文件并传递给爬取函数
        with open(output_file, "a", encoding="utf-8") as f:
            extract_internal_links(domain, f, file_lock)

            # 写入XML尾部
            f.write('</urlset>\n')

        # 计算总耗时
        end_time = time.time()
        duration = end_time - start_time

        # 格式化时间
        minutes, seconds = divmod(duration, 60)

        print(f"Sitemap 生成完成: {os.path.abspath(output_file)}")
        print(f"总耗时: {int(minutes)}分 {seconds:.2f}秒")

        # 返回文件路径供后续使用
        return output_file

    except Exception as e:
        print(f"生成 Sitemap 时发生严重错误: {e!r}")

        # 清理不完整的文件
        if os.path.exists(output_file):
            try:
                with open(output_file, "w", encoding="utf-8") as f:
                    f.write('<?xml version="1.0" encoding="UTF-8"?>\n')
                    f.write('<!-- Sitemap generation failed -->\n')
                print(f"Sitemap 文件生成失败，已清理: {output_file}")
            except IOError:
                print(f"无法清理失败的 Sitemap 文件: {output_file}")

        # 重新抛出异常或返回None
        return None


if __name__ == "__main__":
    try:
        # 检查依赖
        check_dependencies()

        # 默认使用命令行参数，否则使用默认值
        if len(sys.argv) > 1:
            domain = sys.argv[1]
        else:
            domain = "https://www.baidu.com"

        # 可选的深度参数
        if len(sys.argv) > 2:
            try:
                depth = int(sys.argv[2])
                print(f"使用自定义爬取深度: {depth}")
            except ValueError:
                depth = None
        else:
            depth = None

        # 使用Selenium选项
        if len(sys.argv) > 3:
            use_selenium_arg = sys.argv[3].lower()
            if use_selenium_arg in ('true', '1', 'yes', 'y'):
                USE_SELENIUM = True
            elif use_selenium_arg in ('false', '0', 'no', 'n'):
                USE_SELENIUM = False
            print(f"使用Selenium: {USE_SELENIUM}")

        # 生成sitemap
        generate_sitemap(domain, max_depth=depth)

    except ImportError as e:
        print(f"错误: {e}")
    except ValueError as e:
        print(f"输入错误: {e}")
    except Exception as e:
        print(f"发生未预料的错误: {e}")
        import traceback

        traceback.print_exc()