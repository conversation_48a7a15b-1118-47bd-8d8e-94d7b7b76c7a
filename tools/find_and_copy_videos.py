import os
import shutil

def find_and_copy_videos(source_dir, target_dir):
    # 常见视频文件扩展名
    video_extensions = ['.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm', '.m4v', '.mpg', '.mpeg', '.3gp']
    
    # 确保目标目录存在
    os.makedirs(target_dir, exist_ok=True)
    
    # 计数器
    copied_count = 0
    
    # 递归遍历源目录
    for root, _, files in os.walk(source_dir):
        for file in files:
            file_path = os.path.join(root, file)
            file_ext = os.path.splitext(file)[1].lower()
            
            # 检查是否为视频文件
            if file_ext in video_extensions:
                # 构造目标路径
                dest_path = os.path.join(target_dir, file)
                
                # 如果目标中已存在同名文件，添加数字后缀
                if os.path.exists(dest_path):
                    filename, ext = os.path.splitext(file)
                    counter = 1
                    while os.path.exists(os.path.join(target_dir, f"{filename}_{counter}{ext}")):
                        counter += 1
                    dest_path = os.path.join(target_dir, f"{filename}_{counter}{ext}")
                
                # 复制文件
                shutil.copy2(file_path, dest_path)
                copied_count += 1
                print(f"已复制: {file_path} -> {dest_path}")
    
    print(f"完成! 共复制了 {copied_count} 个视频文件")

if __name__ == "__main__":
    # 硬编码源目录和目标目录
    source_directory = "/Volumes/外接存储-ST1000LM024 HN-M101MBB_2/云下载"  # 修改为你的源目录路径
    target_directory = "/Volumes/raid0/小仓千代video"    # 修改为你的目标目录路径
    
    find_and_copy_videos(source_directory, target_directory)