#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试演员检测功能的脚本
用于验证 get_codes_requests.py 中的演员数量检测是否正确工作
"""

import sys
from pathlib import Path

# 添加路径以便导入
sys.path.append(str(Path(__file__).parent / "avmanage"))
sys.path.append(str(Path(__file__).parent / "avmanage" / "getav"))

try:
    from get_codes_requests import RequestsCodeScraper
    from av_config import ACTRESS_URL, MULTI_ACTOR_THRESHOLD
    
    def test_actress_detection():
        """测试演员检测功能"""
        print("🧪 测试演员检测功能")
        print("=" * 50)
        
        # 创建调试模式的爬虫实例
        scraper = RequestsCodeScraper(debug_mode=True)
        
        print(f"目标URL: {ACTRESS_URL}")
        print(f"多人作品阈值: {MULTI_ACTOR_THRESHOLD}")
        print()
        
        # 只爬取第一页进行测试
        print("正在爬取第一页进行测试...")
        page_results, next_url = scraper.scrape_page(ACTRESS_URL)
        
        if page_results:
            print("\n📊 测试结果:")
            print("-" * 30)
            
            total_items = sum(len(codes) for codes in page_results.values())
            print(f"总计处理: {total_items} 个作品")
            
            for code_type, codes_list in page_results.items():
                if codes_list:
                    print(f"  - {code_type}: {len(codes_list)} 个")
                    
                    # 显示前3个作品的详细信息
                    if len(codes_list) > 0:
                        print(f"    示例:")
                        for i, (code, date, video_type) in enumerate(codes_list[:3]):
                            print(f"      {i+1}. {code} ({date}) [{video_type}]")
                        if len(codes_list) > 3:
                            print(f"      ... 还有 {len(codes_list) - 3} 个")
                    print()
            
            # 检查分类逻辑
            normal_count = len(page_results['normal'])
            vr_count = len(page_results['vr'])
            collaborative_count = len(page_results['collaborative'])
            collection_count = len(page_results['collection'])
            multi_actor_count = len(page_results['multi_actor'])
            
            print("🔍 分类验证:")
            print(f"  - 单体作品: {normal_count}")
            print(f"  - VR作品: {vr_count}")
            print(f"  - 共演作品: {collaborative_count}")
            print(f"  - 合集作品: {collection_count}")
            print(f"  - 多人作品总计: {multi_actor_count}")
            
            if collaborative_count + collection_count == multi_actor_count:
                print("  ✅ 多人作品分类统计一致")
            else:
                print("  ❌ 多人作品分类统计不一致！")
            
            if next_url:
                print(f"\n下一页URL: {next_url}")
            else:
                print("\n没有找到下一页")
                
        else:
            print("❌ 测试失败：无法获取页面数据")
            
    if __name__ == "__main__":
        test_actress_detection()
        
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("请确保 avmanage/getav/ 目录下有正确的配置文件")
except Exception as e:
    print(f"❌ 测试过程中发生错误: {e}")
    import traceback
    traceback.print_exc()
