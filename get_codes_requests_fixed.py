#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复版：基于 requests + BeautifulSoup 的番号获取脚本
修复了完整标题提取问题
"""

import requests
from bs4 import BeautifulSoup
import re
import time
from datetime import datetime
from pathlib import Path
import sys
import os
from urllib.parse import urljoin
from collections import defaultdict
from tqdm import tqdm

# 添加父目录到路径，以便导入配置
sys.path.append(str(Path(__file__).parent / "avmanage"))
from av_config import *

class RequestsCodeScraperFixed:
    """修复版的基于 requests + BeautifulSoup 的番号爬虫类"""
    
    def __init__(self):
        # 使用与 get_magnet_sukebei.py 相同的 headers 配置
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9,ja;q=0.8,zh-CN;q=0.7',
        }
        self.session = requests.Session()
        self.session.headers.update(self.headers)
        
        # 请求间隔配置（秒）
        self.request_delay = 2
        
    def is_date_in_range(self, release_date_str, date_range_filter_tuple):
        """检查日期是否在指定的日期范围内"""
        if not date_range_filter_tuple or (not date_range_filter_tuple[0] and not date_range_filter_tuple[1]):
            return True

        if not release_date_str:
            return False

        try:
            item_date = datetime.strptime(release_date_str, "%Y-%m-%d").date()
        except ValueError:
            print(f"警告：作品发行日期 '{release_date_str}' 格式无效，无法进行日期范围比较。将跳过此项。")
            return False

        start_date_str, end_date_str = date_range_filter_tuple
        
        start_date_obj = None
        if start_date_str:
            try:
                start_date_obj = datetime.strptime(start_date_str, "%Y-%m-%d").date()
            except ValueError:
                print(f"警告：配置的开始日期 '{start_date_str}' 格式无效，此日期边界将被忽略。")

        end_date_obj = None
        if end_date_str:
            try:
                end_date_obj = datetime.strptime(end_date_str, "%Y-%m-%d").date()
            except ValueError:
                print(f"警告：配置的结束日期 '{end_date_str}' 格式无效，此日期边界将被忽略。")

        if start_date_obj and item_date < start_date_obj:
            return False
        
        if end_date_obj and item_date > end_date_obj:
            return False
            
        return True
    
    def extract_full_title(self, item):
        """提取完整的作品标题，优先从title属性获取"""
        try:
            # 方法1: 查找带有title属性的链接元素（最优先）
            title_links = item.find_all('a', title=True)
            for link in title_links:
                title_attr = link.get('title', '').strip()
                # 检查是否是作品标题（长度合理且不是简单的导航文本）
                if (title_attr and 
                    len(title_attr) > 15 and  # 作品标题通常较长
                    not title_attr in ['続きを読む', 'FANZA', '詳細を見る'] and  # 排除导航文本
                    not re.match(r'^[A-Z]{2,5}[-_]?\d{2,5}$', title_attr)):  # 不是番号
                    return title_attr
            
            # 方法2: 查找img元素的alt属性（备选方案）
            img_elements = item.find_all('img', alt=True)
            for img in img_elements:
                alt_text = img.get('alt', '').strip()
                if (alt_text and 
                    len(alt_text) > 15 and
                    not re.match(r'^[A-Z]{2,5}[-_]?\d{2,5}$', alt_text)):
                    return alt_text
            
            return None
            
        except Exception as e:
            print(f"提取完整标题时出错: {e}")
            return None
    
    def extract_code_info_from_item(self, item):
        """从作品项中提取番号信息"""
        try:
            # 查找番号
            code_text = ""
            meta_items = item.find_all('li')
            
            for meta_item in meta_items:
                if 'fa-circle-o' in str(meta_item) or meta_item.find(class_='fa-circle-o'):
                    code_text = meta_item.get_text().strip()
                    if code_text:
                        break
            
            if not code_text:
                return None
            
            # 查找发布日期
            release_date = ""
            date_elements = item.find_all(class_='fa-clock-o')
            if date_elements:
                date_parent = date_elements[0].parent
                if date_parent:
                    release_date = date_parent.get_text().strip()
            
            # 查找完整的作品标题
            full_title = self.extract_full_title(item)
            
            # 查找视频类型和VR标识
            is_vr = False
            video_type = full_title if full_title else "未知类型"
            
            # 检查是否为VR作品
            if full_title and "VR" in full_title.upper():
                is_vr = True
            
            # 查找演员数量（默认为1，单体作品）
            actress_count = 1
            
            return code_text, release_date, video_type, is_vr, actress_count
            
        except Exception as e:
            print(f"提取番号信息时出错: {e}")
            return None
    
    def scrape_page(self, url):
        """爬取单个页面的番号信息"""
        try:
            print(f"正在访问: {url}")
            response = self.session.get(url)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 查找作品列表项
            archive_items = soup.find_all(class_="archive-list")
            
            if not archive_items:
                print("未找到 archive-list 元素")
                return None, None
                
            print(f"找到 {len(archive_items)} 个作品项")
            
            page_results = {
                'normal': [],
                'vr': [],
                'multi_actor': [],
                'collaborative': [],
                'collection': []
            }
            
            for item in archive_items:
                try:
                    code_info = self.extract_code_info_from_item(item)
                    if code_info:
                        code_text, release_date, video_type, is_vr, actress_count = code_info
                        
                        # 应用日期过滤器
                        if not self.is_date_in_range(release_date, DATE_RANGE_FILTER):
                            print(f"番号 {code_text} (日期: {release_date}) 不在配置的日期范围内，将跳过。")
                            continue
                        
                        if is_vr:
                            page_results['vr'].append((code_text, release_date, video_type))
                            print(f"找到VR番号: {code_text}, 日期: {release_date}")
                        else:
                            video_type_with_label = f"{video_type} (单体)"
                            page_results['normal'].append((code_text, release_date, video_type_with_label))
                            print(f"找到普通番号: {code_text}, 日期: {release_date}")
                                
                except Exception as e:
                    print(f"处理作品项时出错: {e}")
                    continue
            
            # 查找下一页URL
            next_url = None
            try:
                next_page_element = soup.find('a', class_='next page-numbers')
                if next_page_element:
                    next_url = next_page_element.get('href')
                    if next_url:
                        next_url = urljoin(url, next_url)
            except Exception as e:
                print(f"查找下一页时出错: {e}")
            
            return page_results, next_url
            
        except Exception as e:
            print(f"爬取页面时出错: {e}")
            return None, None
    
    def scrape_all_pages(self, start_url, max_pages=None):
        """爬取所有页面的番号信息"""
        print(f"开始爬取番号，起始URL: {start_url}")
        
        all_codes_info = {
            'normal': [],
            'vr': [],
            'multi_actor': [],
            'collaborative': [],
            'collection': []
        }
        
        current_url = start_url
        current_page = 1
        
        # 使用进度条
        pbar = tqdm(desc="爬取进度", unit="页")
        
        try:
            while current_url:
                if max_pages and current_page > max_pages:
                    print(f"已达到最大页数限制: {max_pages}")
                    break
                
                pbar.set_description(f"处理第 {current_page} 页")
                
                page_results, next_url = self.scrape_page(current_url)
                
                if page_results:
                    # 合并结果
                    for code_type, codes_list in page_results.items():
                        all_codes_info[code_type].extend(codes_list)
                    
                    # 更新进度条信息
                    total_codes = sum(len(codes) for codes in all_codes_info.values())
                    pbar.set_postfix({
                        "总计": total_codes,
                        "普通": len(all_codes_info['normal']),
                        "VR": len(all_codes_info['vr'])
                    })
                
                current_url = next_url
                current_page += 1
                pbar.update(1)
                
                # 添加请求间隔
                if current_url:
                    time.sleep(self.request_delay)
                    
        finally:
            pbar.close()
        
        return all_codes_info

def main():
    """主函数"""
    print("✨ 修复版：基于 requests + BeautifulSoup 的番号获取工具")
    print("=" * 60)
    
    # 从配置文件获取必要的配置项
    actress_url = ACTRESS_URL
    file_paths = get_file_paths()
    unified_output_file = file_paths["unified_codes_file"]
    
    print(f"演员页面URL: {actress_url}")
    print(f"统一番号输出文件: {unified_output_file}")
    print(f"日期过滤范围: {DATE_RANGE_FILTER}")
    
    # 创建爬虫实例
    scraper = RequestsCodeScraperFixed()
    
    try:
        # 爬取所有页面
        all_codes_info = scraper.scrape_all_pages(actress_url)
        
        # 按发布日期对番号进行排序（新的在前）
        for code_type in all_codes_info:
            all_codes_info[code_type] = sorted(
                all_codes_info[code_type],
                key=lambda x: x[1] if x[1] else "0000-00-00",
                reverse=True
            )
        
        # 强制创建新的输出文件
        output_file_path = Path(unified_output_file)
        output_file_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 保存所有番号到统一文件
        with open(output_file_path, 'w', encoding='utf-8') as f:
            for code_type in ['normal', 'vr', 'collaborative', 'collection']:
                if all_codes_info[code_type]:  # 只写入有数据的类型
                    type_name = AVAILABLE_CODE_TYPES.get(code_type, code_type)
                    f.write(f"# {type_name}\n")
                    for code, date, video_type in all_codes_info[code_type]:
                        f.write(f"{code} ({date}) [{video_type}]\n")
                    f.write("\n")
        
        # 输出统计信息
        print("\n" + "=" * 60)
        print("爬取完成！统计信息:")
        print("=" * 60)
        
        total_codes = sum(len(codes) for codes in all_codes_info.values())
        print(f"总计获取番号: {total_codes} 个")
        
        for code_type, codes_list in all_codes_info.items():
            if codes_list:
                type_name = AVAILABLE_CODE_TYPES.get(code_type, code_type)
                print(f"  - {type_name}: {len(codes_list)} 个")
        
        print(f"\n已保存到文件: {output_file_path}")
        
        # 显示前几个示例
        if all_codes_info['normal']:
            print(f"\n前3个普通作品示例:")
            for i, (code, date, title) in enumerate(all_codes_info['normal'][:3]):
                print(f"  {i+1}. {code} ({date}) [{title[:50]}{'...' if len(title) > 50 else ''}]")
        
    except Exception as e:
        print(f"程序发生异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
