#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import subprocess
import argparse
from pathlib import Path

# 导入配置
from av_config import *

def run_get_codes():
    """运行get_codes.py获取番号列表"""
    print("\n==== 步骤1: 获取番号列表 ====")
    
    try:
        script_path = Path(__file__).parent / "getav" / "get_codes.py"
        result = subprocess.run([sys.executable, str(script_path)], check=True)
        if result.returncode == 0:
            print("番号列表获取成功")
            return True
        else:
            print("番号列表获取失败")
            return False
    except Exception as e:
        print(f"运行get_codes.py时出错: {e}")
        return False

def run_list_files():
    """运行list_files.py列出文件"""
    print("\n==== 步骤2: 获取文件列表 ====")
    
    try:
        script_path = Path(__file__).parent / "getav" / "list_files.py"
        
        # 直接运行脚本，不需要任何参数
        result = subprocess.run([sys.executable, str(script_path)], check=True)
        if result.returncode == 0:
            print("文件列表生成成功")
            return True
        else:
            print("文件列表生成失败")
            return False
    except Exception as e:
        print(f"运行list_files.py时出错: {e}")
        return False

def run_check_missing_codes():
    """运行check_missing_codes.py检查缺失番号"""
    print("\n==== 步骤3: 检查缺失番号 ====")
    
    try:
        script_path = Path(__file__).parent / "getav" / "check_missing_codes.py"
        subprocess.run([sys.executable, str(script_path)], check=True)
        print("缺失番号检查完成")
        return True
    except Exception as e:
        print(f"运行check_missing_codes.py时出错: {e}")
        return False

def run_get_magnet():
    """运行get_magnet.py获取磁力链接"""
    print("\n==== 步骤4: 获取磁力链接 ====")
    
    try:
        script_path = Path(__file__).parent / "getav" / "get_magnet.py"
        subprocess.run([sys.executable, str(script_path)], check=True)
        print("磁力链接获取完成")
        return True
    except Exception as e:
        print(f"运行get_magnet.py时出错: {e}")
        return False

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="AV管理工具 - 串联番号获取、文件检查和磁力链接获取功能")
    parser.add_argument("--step", type=int, choices=[1, 2, 3, 4], help="仅运行特定步骤: 1=获取番号, 2=列出文件, 3=检查缺失, 4=获取磁力链接")
    return parser.parse_args()

def main():
    """主函数"""
    args = parse_arguments()
    
    # 确保输出目录存在
    ensure_output_dir()
    
    # 执行流程
    step_functions = [
        (run_get_codes, "获取番号列表"),
        (run_list_files, "获取文件列表"),
        (run_check_missing_codes, "检查缺失番号"),
        (run_get_magnet, "获取磁力链接")
    ]
    
    # 如果指定了特定步骤，只运行该步骤
    if args.step:
        print(f"仅运行步骤 {args.step}: {step_functions[args.step-1][1]}")
        step_functions[args.step-1][0]()
        return
    
    # 否则运行完整流程
    print("开始运行完整流程...\n")
    for i, (func, desc) in enumerate(step_functions, 1):
        print(f"步骤 {i}/{len(step_functions)}: {desc}")
        if not func():
            print(f"步骤 {i} 失败，中止执行")
            break
    
    print("\n所有步骤已完成")

if __name__ == "__main__":
    main()