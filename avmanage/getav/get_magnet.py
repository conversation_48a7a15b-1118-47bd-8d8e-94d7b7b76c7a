#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException
import os
import time
import re
import sys
from pathlib import Path
from tqdm import tqdm

# 添加父目录到路径，以便导入配置
sys.path.append(str(Path(__file__).parent.parent))
from av_config import *

def read_missing_codes_from_unified_file():
    """从统一缺失番号文件读取所有缺失的番号，保留类型信息"""
    print("\n" + "="*50)
    print("从统一缺失番号文件读取番号...")
    
    file_paths = get_file_paths()
    unified_missing_codes_file = file_paths["unified_missing_codes_file"]
    
    if not os.path.exists(unified_missing_codes_file):
        print(f"统一缺失番号文件 {unified_missing_codes_file} 不存在")
        print("请先运行 check_missing_codes.py 生成缺失番号文件")
        return {}, file_paths["magnets_file"]
    
    # 使用字典保存不同类型的番号
    missing_codes_by_type = {}
    current_type = None
    
    with open(unified_missing_codes_file, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            # 跳过空行
            if not line:
                continue
            
            # 检查是否是类型标题行
            if line.startswith('#'):
                current_type = line[1:].strip()  # 去掉 # 获取类型名称
                if current_type not in missing_codes_by_type:
                    missing_codes_by_type[current_type] = []
                continue
            
            # 如果有当前类型，将番号添加到对应类型中
            if current_type:
                missing_codes_by_type[current_type].append(line)
    
    total_codes = sum(len(codes) for codes in missing_codes_by_type.values())
    print(f"从统一文件 {unified_missing_codes_file} 读取到 {total_codes} 个缺失番号")
    for type_name, codes in missing_codes_by_type.items():
        print(f"  - {type_name}: {len(codes)} 个")
    
    return missing_codes_by_type, file_paths["magnets_file"]

def read_existing_magnets(output_file):
    """读取已存在的磁力链接文件，返回一个字典，键为番号，值为磁力链接"""
    existing_magnets = {}
    if os.path.exists(output_file):
        try:
            with open(output_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if not line:
                        continue
                    # 提取番号和磁力链接
                    parts = line.split(':', 1)
                    if len(parts) == 2:
                        code = parts[0].strip()
                        magnet = parts[1].strip()
                        # 只保存有效的磁力链接，忽略"未找到结果"等无效链接
                        if magnet.startswith("magnet:?xt=urn:btih:"):
                            existing_magnets[code] = magnet
            print(f"从 {output_file} 读取了 {len(existing_magnets)} 个已有的有效磁力链接")
        except Exception as e:
            print(f"读取已有磁力链接文件时出错: {e}")
    return existing_magnets


def main():
    # 从统一缺失番号文件读取番号
    codes_by_type, output_file = read_missing_codes_from_unified_file()
    base_url = MAGNET_SEARCH_URL
    
    if not codes_by_type:
        print("没有缺失番号，跳过获取磁力链接步骤")
        return
    
    # 将所有类型的番号合并为一个列表用于处理
    all_codes = []
    for codes_list in codes_by_type.values():
        all_codes.extend(codes_list)
    
    total_codes = len(all_codes)
    print(f"共读取到 {total_codes} 个缺失番号")
    
    # 读取已有的磁力链接
    existing_magnets = read_existing_magnets(output_file)
    
    # 筛选出需要获取的番号（排除已有有效磁力链接的番号）
    codes_to_fetch = []
    codes_type_map = {}  # 记录每个番号对应的类型
    cached_magnets_by_type = {}  # 按类型存储已缓存的磁力链接
    
    # 初始化缓存字典
    for type_name in codes_by_type.keys():
        cached_magnets_by_type[type_name] = []
    
    print("\n检查已有磁力链接...")
    # 使用进度条显示检查进度
    all_codes_with_type = []
    for type_name, codes_list in codes_by_type.items():
        for code in codes_list:
            all_codes_with_type.append((code, type_name))
    
    with tqdm(total=len(all_codes_with_type), desc="检查进度", unit="个番号") as pbar:
        for code, type_name in all_codes_with_type:
            if code in existing_magnets:
                cached_magnets_by_type[type_name].append(f"{code}: {existing_magnets[code]}")
            else:
                codes_to_fetch.append(code)
                codes_type_map[code] = type_name
            pbar.update(1)
    
    total_cached = sum(len(magnets) for magnets in cached_magnets_by_type.values())
    print(f"共有 {total_cached} 个番号已有磁力链接，{len(codes_to_fetch)} 个番号需要获取")
    
    # 如果所有番号都已有磁力链接，则按类型输出并返回
    if not codes_to_fetch:
        print("所有番号都已有磁力链接，按类型重新整理输出文件")
        with open(output_file, 'w', encoding='utf-8') as f:
            for type_name, magnets_list in cached_magnets_by_type.items():
                if magnets_list:
                    f.write(f"# {type_name}\n")
                    for magnet in magnets_list:
                        f.write(f"{magnet}\n")
                    f.write("\n")
        print(f"已按类型重新整理磁力链接文件: {output_file}")
        return

    # 启动Edge浏览器
    driver = webdriver.Edge()
    magnets = []  # 存储所有找到的磁力链接

    try:
        # 首先访问主页
        print(f"正在访问主页: {base_url}")
        driver.get(base_url)
        
        # 循环处理每个需要获取的番号
        print(f"\n开始获取 {len(codes_to_fetch)} 个番号的磁力链接...")
        
        # 使用进度条显示获取进度
        with tqdm(total=len(codes_to_fetch), desc="获取进度", unit="个番号", ncols=100) as pbar:
            for idx, code in enumerate(codes_to_fetch, 1):
                # 更新进度条描述
                pbar.set_description(f"处理 {code} [{idx}/{len(codes_to_fetch)}]")
                try:
                    # 等待搜索框加载
                    wait = WebDriverWait(driver, 30)
                    search_input = wait.until(EC.presence_of_element_located((By.NAME, "keyword")))
                    
                    # 在搜索框中输入番号
                    search_input.clear()
                    search_input.send_keys(f"hhd800@{code}")
                    
                    # 点击搜索按钮
                    search_button = driver.find_element(By.CSS_SELECTOR, "button[type='submit']")
                    search_button.click()

                    # 等待页面加载完成，可能会有Cloudflare盾，等待时间设长一些
                    wait = WebDriverWait(driver, 60)  # 增加等待时间到60秒
                    
                    # 等待内容加载完成（等待面板出现）
                    wait.until(EC.presence_of_element_located((By.CLASS_NAME, "panel-default")))
                
                    # 寻找包含特定文件名的结果
                    target_text = f"hhd800.com@{code}"
                    
                    # 查找所有的面板
                    panels = driver.find_elements(By.CLASS_NAME, "panel-default")
                    target_panel = None
                    detail_url = None
                    
                    for panel in panels:
                        if target_text in panel.text:
                            target_panel = panel
                            # 在panel-footer中找到磁力链接按钮的href
                            panel_footer = panel.find_element(By.CLASS_NAME, "panel-footer")
                            magnet_link_element = panel_footer.find_element(By.CSS_SELECTOR, "a[title='Download using magnet']")
                            detail_url = magnet_link_element.get_attribute("href")
                            break
                
                    if detail_url:
                        # 修改: 使用JavaScript移除target属性，让链接在当前标签页打开
                        driver.execute_script("arguments[0].removeAttribute('target')", magnet_link_element)
                        
                        # 点击修改后的链接
                        magnet_link_element.click()
                    
                        # 等待页面加载
                        time.sleep(5)
                        
                        # 直接从页面源码中提取磁力链接
                        page_source = driver.page_source
                        # 使用正则表达式查找磁力链接
                        magnet_pattern = r'(magnet:\?xt=urn:btih:[a-zA-Z0-9]+)'
                        matches = re.findall(magnet_pattern, page_source)
                        
                        if matches:
                            magnet_link = matches[0]
                            magnets.append(f"{code}: {magnet_link}")
                            pbar.set_postfix({"状态": "成功"})
                        else:
                            # 如果未找到，尝试其他格式的磁力链接
                            alternative_pattern = r'(magnet:\?[^"\'<>\s]+)'
                            alt_matches = re.findall(alternative_pattern, page_source)
                            if alt_matches:
                                magnet_link = alt_matches[0]
                                magnets.append(f"{code}: {magnet_link}")
                                pbar.set_postfix({"状态": "成功(替代)"})
                            else:
                                magnets.append(f"{code}: 未找到磁力链接")
                                pbar.set_postfix({"状态": "未找到"})
                    else:
                        magnets.append(f"{code}: 未找到结果")
                        pbar.set_postfix({"状态": "无结果"})
                    
                    # 回到主页准备下一次搜索
                    driver.get(base_url)
                    
                except TimeoutException as te:
                    magnets.append(f"{code}: 处理超时")
                    pbar.set_postfix({"状态": "超时"})
                except Exception as e:
                    magnets.append(f"{code}: 处理错误 - {str(e)}")
                    pbar.set_postfix({"状态": "错误"})
                    
                    # 回到主页尝试继续处理下一个番号
                    try:
                        driver.get(base_url)
                    except:
                        pass
                
                # 更新进度条
                pbar.update(1)

        # 将新获取的磁力链接按类型分组
        new_magnets_by_type = {}
        for type_name in codes_by_type.keys():
            new_magnets_by_type[type_name] = []
        
        # 将新获取的磁力链接分配到对应类型
        for magnet_entry in magnets:
            if ':' in magnet_entry:
                code = magnet_entry.split(':', 1)[0].strip()
                # 使用预先建立的映射查找该番号属于哪个类型
                found_type = codes_type_map.get(code)
                
                if found_type:
                    new_magnets_by_type[found_type].append(magnet_entry)
                else:
                    # 如果找不到类型，放到第一个类型中
                    first_type = list(codes_by_type.keys())[0] if codes_by_type else None
                    if first_type:
                        new_magnets_by_type[first_type].append(magnet_entry)
        
        # 合并缓存的和新获取的磁力链接
        for type_name in cached_magnets_by_type.keys():
            if type_name in new_magnets_by_type:
                cached_magnets_by_type[type_name].extend(new_magnets_by_type[type_name])
        
        # 按类型保存磁力链接
        print(f"\n" + "="*50)
        print(f"保存磁力链接到文件: {output_file}")
        with open(output_file, 'w', encoding='utf-8') as f:
            for type_name, magnets_list in cached_magnets_by_type.items():
                if magnets_list:  # 只写入有数据的类型
                    f.write(f"# {type_name}\n")
                    for magnet in magnets_list:
                        f.write(f"{magnet}\n")
                    f.write("\n")  # 类型之间添加空行
        
        total_magnets = sum(len(magnets_list) for magnets_list in cached_magnets_by_type.values())
        print(f"\n保存完成！共保存了 {total_magnets} 个结果")
        print("\n各类型统计：")
        for type_name, magnets_list in cached_magnets_by_type.items():
            if magnets_list:
                print(f"  - {type_name}: {len(magnets_list)} 个")

    except Exception as e:
        print(f"主程序发生错误: {e}")
    finally:
        # 在程序结束前按类型保存结果（即使发生错误也保存已获取的结果）
        if magnets and not os.path.exists(output_file):
            try:
                # 将新获取的磁力链接按类型分组
                new_magnets_by_type = {}
                for type_name in codes_by_type.keys():
                    new_magnets_by_type[type_name] = []
                
                for magnet_entry in magnets:
                    if ':' in magnet_entry:
                        code = magnet_entry.split(':', 1)[0].strip()
                        found_type = None
                        for type_name, codes_list in codes_by_type.items():
                            if code in codes_list:
                                found_type = type_name
                                break
                        
                        if found_type:
                            new_magnets_by_type[found_type].append(magnet_entry)
                
                # 合并并保存
                for type_name in cached_magnets_by_type.keys():
                    if type_name in new_magnets_by_type:
                        cached_magnets_by_type[type_name].extend(new_magnets_by_type[type_name])
                
                with open(output_file, 'w', encoding='utf-8') as f:
                    for type_name, magnets_list in cached_magnets_by_type.items():
                        if magnets_list:
                            f.write(f"# {type_name}\n")
                            for magnet in magnets_list:
                                f.write(f"{magnet}\n")
                            f.write("\n")
                
                total_magnets = sum(len(magnets_list) for magnets_list in cached_magnets_by_type.values())
                print(f"在程序结束前按类型保存了 {total_magnets} 个结果")
            except Exception as e:
                print(f"保存结果时发生错误: {e}")
        
        # 关闭浏览器
        time.sleep(3)  # 给用户一些时间查看结果
        driver.quit()
        
        # 不再需要清理临时文件，因为我们直接从统一文件读取
        
        # 删除本地HTML缓存
        print("\n清理HTML缓存文件...")
        cache_files = [f for f in os.listdir() if f.endswith('.html')]
        if cache_files:
            with tqdm(total=len(cache_files), desc="删除缓存", unit="个文件") as pbar:
                for cache_file in cache_files:
                    try:
                        os.remove(cache_file)
                        pbar.update(1)
                    except Exception as e:
                        pbar.set_postfix({"错误": str(e)})
                        pbar.update(1)
        else:
            print("没有找到HTML缓存文件")
        
        print("\n" + "="*50)
        print("任务完成！")

if __name__ == "__main__":
    main()
