#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import os
import time
import re
from collections import defaultdict
import sys
from pathlib import Path
from datetime import datetime # 新增导入

# 添加父目录到路径，以便导入配置
sys.path.append(str(Path(__file__).parent.parent))
from av_config import *

def is_date_in_range(release_date_str, date_range_filter_tuple):
    """
    检查日期是否在指定的日期范围内。
    :param release_date_str: 作品的发行日期字符串 (YYYY-MM-DD)
    :param date_range_filter_tuple: 包含开始和结束日期字符串的元组 (start_date_str, end_date_str)
    :return: 如果日期在范围内或过滤器未激活，则返回 True，否则返回 False
    """
    # 如果过滤器未设置或为空，则视为通过
    if not date_range_filter_tuple or (not date_range_filter_tuple[0] and not date_range_filter_tuple[1]):
        return True

    # 如果作品的发行日期为空，则无法满足日期范围要求（除非范围本身也无效到不设限）
    if not release_date_str:
        return False

    try:
        item_date = datetime.strptime(release_date_str, "%Y-%m-%d").date()
    except ValueError:
        print(f"警告：作品发行日期 '{release_date_str}' 格式无效，无法进行日期范围比较。将跳过此项。")
        return False

    start_date_str, end_date_str = date_range_filter_tuple
    
    start_date_obj = None
    if start_date_str:
        try:
            start_date_obj = datetime.strptime(start_date_str, "%Y-%m-%d").date()
        except ValueError:
            print(f"警告：配置的开始日期 '{start_date_str}' 格式无效，此日期边界将被忽略。")

    end_date_obj = None
    if end_date_str:
        try:
            end_date_obj = datetime.strptime(end_date_str, "%Y-%m-%d").date()
        except ValueError:
            print(f"警告：配置的结束日期 '{end_date_str}' 格式无效，此日期边界将被忽略。")

    if start_date_obj and item_date < start_date_obj:
        return False
    
    if end_date_obj and item_date > end_date_obj:
        return False
        
    return True

def main():
    # 从配置文件获取必要的配置项
    actress_url = ACTRESS_URL
    file_paths = get_file_paths()
    output_file = file_paths["unified_codes_file"]  # 使用统一的codes文件
    unified_output_file = file_paths["unified_codes_file"]
    
    print(f"演员页面URL: {actress_url}")
    print(f"普通番号输出文件: {output_file}")
    print(f"统一番号输出文件: {unified_output_file}")
    print(f"作品分类规则: 1人=单体，2-{MULTI_ACTOR_THRESHOLD}人=共演，>{MULTI_ACTOR_THRESHOLD}人=合集")

    # 启动Edge浏览器
    driver = webdriver.Edge()
    # 存储所有找到的番号信息，格式为 {番号: (日期, 类型)}
    all_codes_info = {}
    all_vr_codes_info = {}
    all_multi_actor_codes_info = {}
    all_collaborative_codes_info = {}
    all_collection_codes_info = {}

    try:
        # 访问起始页面
        print(f"正在访问起始页面: {actress_url}")
        driver.get(actress_url)
        time.sleep(3)  # 给页面加载一些时间
        
        current_page = 1
        
        while True:  # 修改为无限循环，直到没有下一页
            print(f"\n正在处理第 {current_page} 页")
            
            try:
                # 等待页面加载完成
                wait = WebDriverWait(driver, 30)
                wait.until(EC.presence_of_element_located((By.CLASS_NAME, "archive-list")))
                
                print("页面加载完成，开始提取番号")
                
                # 找到所有作品列表项
                archive_items = driver.find_elements(By.CLASS_NAME, "archive-list")
                page_codes = []
                page_vr_codes = []
                page_multi_actor_codes = []
                page_collaborative_codes = []
                page_collection_codes = []
                
                # 判断当前页面是否包含VR作品
                for item in archive_items:
                    is_vr = False
                    is_multi_actor = False
                    is_collaborative = False
                    is_collection = False
                    release_date = ""
                    video_type = "未知类型"
                    code_text = ""
                    
                    try:
                        # 查找标签，检查是否包含VR字样以及获取类型
                        categories = item.find_elements(By.CSS_SELECTOR, ".post-meta li a")
                        for category in categories:
                            category_text = category.text
                            if "VR" in category_text:
                                is_vr = True
                            # 获取视频类型
                            video_type = category_text
                    except Exception as e:
                        pass  # 无法找到分类，假定不是VR
                    
                    try:
                        # 获取发布日期
                        date_elements = item.find_elements(By.CSS_SELECTOR, ".post-meta li .fa-clock-o")
                        if date_elements:
                            date_element = date_elements[0].find_element(By.XPATH, "..")
                            if date_element:
                                release_date = date_element.text.strip()
                    except Exception as e:
                        print(f"获取日期失败: {e}") # release_date 保持为 ""
                
                    try:
                        # 从post-meta中查找番号
                        meta_items = item.find_elements(By.CSS_SELECTOR, ".post-meta li")
                        for meta_item in meta_items:
                            if "fa-circle-o" in meta_item.get_attribute("class") or meta_item.find_elements(By.CLASS_NAME, "fa-circle-o"):
                                code_text = meta_item.text.strip()
                                if code_text:
                                    break # 找到番号即跳出内部循环
                        
                        if code_text: # 确保番号已找到
                            # 应用日期过滤器
                            # DATE_RANGE_FILTER 变量应从 av_config 导入
                            # 如果 DATE_RANGE_FILTER 未在 av_config 中定义，下面这行会引发 NameError
                            # 确保 av_config.py 中有 DATE_RANGE_FILTER = ("", "") 或类似定义
                            if not is_date_in_range(release_date, DATE_RANGE_FILTER):
                                print(f"番号 {code_text} (日期: {release_date}) 不在配置的日期范围 {DATE_RANGE_FILTER} 内，将跳过。")
                                continue

                            if is_vr:
                                page_vr_codes.append(code_text)
                                all_vr_codes_info[code_text] = (release_date, video_type)
                                print(f"找到VR番号: {code_text}, 日期: {release_date}, 类型: {video_type}")
                            else:
                                # 判断作品类型 (非VR作品才判断)
                                try:
                                    actress_elements = item.find_elements(By.CSS_SELECTOR, ".post-meta li.actress-name a")
                                    actress_count = len(actress_elements)
                                    if actress_count == 1:
                                        # 单体作品，添加单体标识
                                        video_type = f"{video_type} (单体)"
                                    elif actress_count > 1:
                                        is_multi_actor = True
                                        # 根据人数判断作品类型
                                        if actress_count <= MULTI_ACTOR_THRESHOLD:
                                            is_collaborative = True
                                            collaborative_type = f"{video_type} (共演-{actress_count}人)"
                                        else:
                                            is_collection = True
                                            collection_type = f"{video_type} (合集-{actress_count}人)"
                                        multi_actor_type = collaborative_type if is_collaborative else collection_type
                                except NoSuchElementException:
                                    # 没有找到演员信息元素，按默认类型处理
                                    pass
                                
                                if is_multi_actor:
                                    page_multi_actor_codes.append(code_text)
                                    all_multi_actor_codes_info[code_text] = (release_date, multi_actor_type)
                                    
                                    # 同时添加到相应的分类中
                                    if is_collaborative:
                                        page_collaborative_codes.append(code_text)
                                        all_collaborative_codes_info[code_text] = (release_date, collaborative_type)
                                        print(f"找到共演作品番号: {code_text}, 日期: {release_date}, 类型: {collaborative_type}")
                                    elif is_collection:
                                        page_collection_codes.append(code_text)
                                        all_collection_codes_info[code_text] = (release_date, collection_type)
                                        print(f"找到合集作品番号: {code_text}, 日期: {release_date}, 类型: {collection_type}")
                                else:
                                    page_codes.append(code_text)
                                    all_codes_info[code_text] = (release_date, video_type)
                                    print(f"找到普通番号: {code_text}, 日期: {release_date}, 类型: {video_type}")
                    except Exception as e:
                        print(f"查找番号或判断多人作品失败: {e}")
                
                # 页面处理状态
                print(f"第 {current_page} 页共找到 {len(page_codes)} 个普通番号，{len(page_vr_codes)} 个VR番号，{len(page_multi_actor_codes)} 个多人作品番号")
                print(f"  其中：{len(page_collaborative_codes)} 个共演作品，{len(page_collection_codes)} 个合集作品")
                print(f"累计 {len(all_codes_info)} 个普通番号，{len(all_vr_codes_info)} 个VR番号，{len(all_multi_actor_codes_info)} 个多人作品番号")
                print(f"  其中：{len(all_collaborative_codes_info)} 个共演作品，{len(all_collection_codes_info)} 个合集作品")
                
                # 查找"下一页"按钮并点击
                try:
                    next_page = driver.find_element(By.CSS_SELECTOR, "a.next.page-numbers")
                    next_page_url = next_page.get_attribute("href")
                    print(f"找到下一页: {next_page_url}")
                    driver.get(next_page_url)
                    current_page += 1
                    time.sleep(2)  # 等待页面加载
                except NoSuchElementException:
                    print("没有找到下一页，爬取完成")
                    break
                
            except TimeoutException as te:
                print(f"页面加载超时: {te}")
                break
            except Exception as e:
                print(f"发生错误: {e}")
                break

        # 按发布日期对番号进行排序（新的在前）
        sorted_codes = sorted(
            all_codes_info.items(),
            key=lambda x: x[1][0] if x[1][0] else "0000-00-00",
            reverse=True  # 降序排序
        )

        # 统一处理所有类型的番号
        all_codes_combined = {
            "normal": sorted_codes,
            "vr": sorted(all_vr_codes_info.items(), key=lambda x: x[1][0] if x[1][0] else "0000-00-00", reverse=True),
            "collaborative": sorted(all_collaborative_codes_info.items(), key=lambda x: x[1][0] if x[1][0] else "0000-00-00", reverse=True),
            "collection": sorted(all_collection_codes_info.items(), key=lambda x: x[1][0] if x[1][0] else "0000-00-00", reverse=True)
        }
        
        # 不再需要按类型分组，因为现在统一保存到一个文件

        # 保存所有番号到统一文件
        with open(unified_output_file, 'w', encoding='utf-8') as f:
            for code_type, sorted_list in all_codes_combined.items():
                if code_type in ENABLED_CODE_TYPES:
                    f.write(f"# {AVAILABLE_CODE_TYPES[code_type]}\n")
                    for code, (date, video_type) in sorted_list:
                        f.write(f"{code} ({date}) [{video_type}]\n")
                    f.write("\n")  # 每个类别之间添加空行
        print(f"已保存所有番号到统一文件: {unified_output_file}")

    except Exception as e:
        print(f"程序发生异常: {e}")
        
    finally:
        # 关闭浏览器
        driver.quit()
        print("程序执行完毕")

if __name__ == "__main__":
    main()
