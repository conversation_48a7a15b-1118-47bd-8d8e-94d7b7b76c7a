#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import re
import sys
import requests
from pathlib import Path

sys.path.append(str(Path(__file__).parent.parent))
from av_config import *

class MagnetUploader:
    def __init__(self):
        self.auth_url = "http://192.168.31.10:5244/api/auth/login"
        self.upload_url = "http://192.168.31.10:5244/api/fs/add_offline_download"
        self.token = None
        self.file_paths = get_file_paths()
        
    def authenticate(self):
        """登录获取token"""
        login_data = {
            "username": "admin",
            "password": "123qweasdzxc"
        }
        
        print(f"🔐 发送登录请求:")
        print(f"   URL: {self.auth_url}")
        print(f"   请求数据: {json.dumps(login_data, ensure_ascii=False, indent=2)}")
        
        try:
            response = requests.post(self.auth_url, json=login_data)
            response.raise_for_status()
            
            result = response.json()
            if result.get("code") == 200:
                self.token = result.get("data", {}).get("token")
                print(f"登录成功，获取到token")
                return True
            else:
                print(f"登录失败: {result.get('message', '未知错误')}")
                return False
                
        except requests.exceptions.RequestException as e:
            print(f"登录请求失败: {e}")
            return False
        except json.JSONDecodeError as e:
            print(f"解析登录响应失败: {e}")
            return False
    
    def parse_magnets_file(self):
        """解析magnets.txt文件，根据ENABLED_CODE_TYPES筛选磁力链接"""
        magnets_file = self.file_paths["magnets_file"]
        
        if not magnets_file.exists():
            print(f"磁力链接文件不存在: {magnets_file}")
            return []
        
        magnet_links = []
        current_section = None
        
        # 定义番号类型与文件中章节的映射
        section_mapping = {
            "# 普通作品": "normal",
            "# VR作品": "vr", 
            "# 共演作品": "collaborative",
            "# 合集作品": "collection"
        }
        
        print(f"启用的番号类型: {ENABLED_CODE_TYPES}")
        
        try:
            with open(magnets_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    
                    # 检查是否是章节标题
                    if line in section_mapping:
                        current_section = section_mapping[line]
                        print(f"进入章节: {line} -> {current_section}")
                        continue
                    
                    # 跳过空行和注释
                    if not line or line.startswith('#'):
                        continue
                    
                    # 只处理启用的番号类型
                    if current_section not in ENABLED_CODE_TYPES:
                        continue
                    
                    # 提取磁力链接
                    if ':' in line:
                        parts = line.split(':', 1)
                        code = parts[0].strip()
                        magnet_part = parts[1].strip()
                        
                        # 检查是否包含有效的磁力链接
                        if magnet_part.startswith('magnet:?xt=urn:btih:'):
                            magnet_links.append(magnet_part)
                            print(f"添加磁力链接: {code} ({current_section})")
                        else:
                            print(f"跳过无效链接: {code} - {magnet_part}")
                            
        except Exception as e:
            print(f"读取磁力链接文件失败: {e}")
            return []
        
        print(f"总共找到 {len(magnet_links)} 个有效磁力链接")
        return magnet_links
    
    def upload_magnets(self, magnet_links):
        """上传磁力链接到115云盘"""
        if not self.token:
            print("未获取到有效token，请先登录")
            return False
        
        if not magnet_links:
            print("没有找到有效的磁力链接")
            return False
        
        headers = {
            "Authorization": self.token,
            "Content-Type": "application/json"
        }
        
        upload_data = {
            "path": "/115/云下载",
            "urls": magnet_links,
            "tool": "115 Cloud", 
            "delete_policy": "delete_on_upload_succeed"
        }
        
        print(f"📤 发送上传请求:")
        print(f"   URL: {self.upload_url}")
        print(f"   请求头: {json.dumps(headers, ensure_ascii=False, indent=2)}")
        print(f"   请求数据: {json.dumps(upload_data, ensure_ascii=False, indent=2)}")
        
        try:
            print(f"开始上传 {len(magnet_links)} 个磁力链接...")
            response = requests.post(self.upload_url, json=upload_data, headers=headers)
            response.raise_for_status()
            
            result = response.json()
            print(f"上传响应: {result}")
            
            if result.get("code") == 200:
                print("磁力链接上传成功！")
                return True
            else:
                print(f"上传失败: {result.get('message', '未知错误')}")
                return False
                
        except requests.exceptions.RequestException as e:
            print(f"上传请求失败: {e}")
            return False
        except json.JSONDecodeError as e:
            print(f"解析上传响应失败: {e}")
            return False
    
    def run(self):
        """执行完整的上传流程"""
        print("开始磁力链接上传流程...")
        
        # 1. 登录获取token
        if not self.authenticate():
            return False
        
        # 2. 解析磁力链接文件
        magnet_links = self.parse_magnets_file()
        
        # 3. 上传磁力链接
        return self.upload_magnets(magnet_links)


def main():
    uploader = MagnetUploader()
    success = uploader.run()
    
    if success:
        print("\n✅ 磁力链接上传完成！")
    else:
        print("\n❌ 磁力链接上传失败！")


if __name__ == "__main__":
    main()
