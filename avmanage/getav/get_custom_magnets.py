#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException
import os
import time
import re
import sys
from pathlib import Path

# 添加父目录到路径，以便导入配置
sys.path.append(str(Path(__file__).parent.parent))
from av_config import MAGNET_SEARCH_URL

def read_codes_from_file(file_path):
    """从指定文件读取番号"""
    print(f"从文件 {file_path} 读取番号...")
    
    if not os.path.exists(file_path):
        print(f"文件 {file_path} 不存在")
        return []
    
    codes = []
    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            # 跳过空行和注释行
            if line and not line.startswith('#'):
                codes.append(line)
    
    print(f"从文件 {file_path} 读取到 {len(codes)} 个番号")
    return codes

def read_existing_magnets(output_file):
    """读取已存在的磁力链接文件，返回一个字典，键为番号，值为磁力链接"""
    existing_magnets = {}
    if os.path.exists(output_file):
        try:
            with open(output_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if not line or line.startswith('#'):
                        continue
                    # 提取番号和磁力链接
                    parts = line.split(':', 1)
                    if len(parts) == 2:
                        code = parts[0].strip()
                        magnet = parts[1].strip()
                        # 只保存有效的磁力链接，忽略"未找到结果"等无效链接
                        if magnet.startswith("magnet:?xt=urn:btih:"):
                            existing_magnets[code] = magnet
            print(f"从 {output_file} 读取了 {len(existing_magnets)} 个已有的有效磁力链接")
        except Exception as e:
            print(f"读取已有磁力链接文件时出错: {e}")
    return existing_magnets

def get_magnet_link(driver, code, base_url):
    """获取单个番号的磁力链接"""
    try:
        # 等待搜索框加载
        wait = WebDriverWait(driver, 30)
        search_input = wait.until(EC.presence_of_element_located((By.NAME, "keyword")))
        
        # 在搜索框中输入番号
        search_input.clear()
        search_input.send_keys(f"hhd800@{code}")
        
        # 点击搜索按钮
        search_button = driver.find_element(By.CSS_SELECTOR, "button[type='submit']")
        search_button.click()
        
        print(f"执行搜索: hhd800@{code}")

        # 等待页面加载完成，可能会有Cloudflare盾，等待时间设长一些
        wait = WebDriverWait(driver, 60)  # 增加等待时间到60秒
        
        # 等待内容加载完成（等待面板出现）
        wait.until(EC.presence_of_element_located((By.CLASS_NAME, "panel-default")))
        
        print("搜索结果页面加载完成")
        
        # 寻找包含特定文件名的结果
        target_text = f"hhd800.com@{code}"
        
        # 查找所有的面板
        panels = driver.find_elements(By.CLASS_NAME, "panel-default")
        target_panel = None
        detail_url = None
        
        for panel in panels:
            if target_text in panel.text:
                target_panel = panel
                print(f"找到目标结果: {target_text}")
                # 在panel-footer中找到磁力链接按钮的href
                panel_footer = panel.find_element(By.CLASS_NAME, "panel-footer")
                magnet_link_element = panel_footer.find_element(By.CSS_SELECTOR, "a[title='Download using magnet']")
                detail_url = magnet_link_element.get_attribute("href")
                break
        
        if detail_url:
            print(f"准备访问详情页面: {detail_url}")
            
            # 修改: 使用JavaScript移除target属性，让链接在当前标签页打开
            driver.execute_script("arguments[0].removeAttribute('target')", magnet_link_element)
            
            # 点击修改后的链接
            magnet_link_element.click()
            
            print("点击磁力链接按钮")
            
            # 等待页面加载
            time.sleep(5)
            
            # 直接从页面源码中提取磁力链接
            page_source = driver.page_source
            # 使用正则表达式查找磁力链接
            magnet_pattern = r'(magnet:\?xt=urn:btih:[a-zA-Z0-9]+)'
            matches = re.findall(magnet_pattern, page_source)
            
            if matches:
                magnet_link = matches[0]
                print(f"获取到磁力链接: {magnet_link[:60]}...")
                return magnet_link
            else:
                print("未在页面中找到磁力链接")
                
                # 如果未找到，尝试其他格式的磁力链接
                alternative_pattern = r'(magnet:\?[^"\'<>\s]+)'
                alt_matches = re.findall(alternative_pattern, page_source)
                if alt_matches:
                    magnet_link = alt_matches[0]
                    print(f"使用替代方法获取到磁力链接: {magnet_link[:60]}...")
                    return magnet_link
                else:
                    print("使用所有方法均未找到磁力链接")
                    return None
        else:
            print(f"未找到包含 {target_text} 的结果或无法获取详情页URL")
            return None
            
    except TimeoutException as te:
        print(f"处理番号 {code} 时超时: {te}")
        return None
    except Exception as e:
        print(f"处理番号 {code} 时发生错误: {e}")
        return None

def main():
    # 文件路径配置
    input_file = "output/my_codes.txt"
    output_file = "output/my_magnets.txt"
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"输入文件 {input_file} 不存在")
        print("请创建 my_codes.txt 文件，并在其中输入要获取磁力链接的番号，每行一个番号")
        return
    
    # 读取番号
    codes = read_codes_from_file(input_file)
    if not codes:
        print("没有读取到有效的番号")
        return
    
    base_url = MAGNET_SEARCH_URL
    print(f"共读取到 {len(codes)} 个番号")
    
    # 读取已有的磁力链接
    existing_magnets = read_existing_magnets(output_file)
    
    # 筛选出需要获取的番号（排除已有有效磁力链接的番号）
    codes_to_fetch = []
    cached_magnets = []
    
    for code in codes:
        if code in existing_magnets:
            print(f"番号 {code} 已有磁力链接，跳过获取")
            cached_magnets.append(f"{code}: {existing_magnets[code]}")
        else:
            codes_to_fetch.append(code)
    
    print(f"共有 {len(cached_magnets)} 个番号已有磁力链接，{len(codes_to_fetch)} 个番号需要获取")
    
    # 如果所有番号都已有磁力链接，则输出并返回
    if not codes_to_fetch:
        print("所有番号都已有磁力链接，无需重新获取")
        return
    
    # 启动Edge浏览器
    driver = webdriver.Edge()
    magnets = []  # 存储新获取的磁力链接
    
    try:
        # 首先访问主页
        print(f"正在访问主页: {base_url}")
        driver.get(base_url)
        
        # 循环处理每个需要获取的番号
        for i, code in enumerate(codes_to_fetch, 1):
            print(f"\n[{i}/{len(codes_to_fetch)}] 开始处理番号: {code}")
            
            # 获取磁力链接
            magnet_link = get_magnet_link(driver, code, base_url)
            
            if magnet_link:
                magnets.append(f"{code}: {magnet_link}")
                print(f"成功获取番号 {code} 的磁力链接")
            else:
                magnets.append(f"{code}: 未找到磁力链接")
                print(f"未能获取番号 {code} 的磁力链接")
            
            # 回到主页准备下一次搜索
            if i < len(codes_to_fetch):  # 不是最后一个
                driver.get(base_url)
                time.sleep(2)  # 短暂休息避免过于频繁的请求
        
        # 合并缓存的和新获取的磁力链接
        all_magnets = cached_magnets + magnets
        
        # 保存磁力链接到文件
        print(f"\n保存磁力链接到文件: {output_file}")
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(f"# 磁力链接获取时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"# 总计: {len(all_magnets)} 个结果\n\n")
            
            for magnet in all_magnets:
                f.write(f"{magnet}\n")
        
        print(f"共保存了 {len(all_magnets)} 个结果到 {output_file}")
        
        # 统计有效磁力链接数量
        valid_magnets = [m for m in all_magnets if "magnet:?xt=urn:btih:" in m]
        print(f"其中有效磁力链接: {len(valid_magnets)} 个")
        
    except Exception as e:
        print(f"主程序发生错误: {e}")
    finally:
        # 在程序结束前保存结果（即使发生错误也保存已获取的结果）
        if magnets:
            try:
                all_magnets = cached_magnets + magnets
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(f"# 磁力链接获取时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write(f"# 总计: {len(all_magnets)} 个结果（程序异常结束时保存）\n\n")
                    
                    for magnet in all_magnets:
                        f.write(f"{magnet}\n")
                
                print(f"在程序结束前保存了 {len(all_magnets)} 个结果")
            except Exception as e:
                print(f"保存结果时发生错误: {e}")
        
        # 关闭浏览器
        time.sleep(3)  # 给用户一些时间查看结果
        driver.quit()

if __name__ == "__main__":
    main()
