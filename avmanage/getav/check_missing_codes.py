#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
import sys
import argparse
from pathlib import Path

# 添加父目录到路径，以便导入配置
sys.path.append(str(Path(__file__).parent.parent))
from av_config import *

def read_codes_from_unified_file(file_path, enabled_types=None):
    """从统一番号文件读取指定类型的番号，返回一个集合"""
    if enabled_types is None:
        enabled_types = ENABLED_CODE_TYPES
    
    codes = set()
    current_type = None
    current_type_key = None
    
    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            
            # 跳过空行
            if not line:
                continue
            
            # 检查是否是类型标题行
            if line.startswith('#'):
                type_name = line[1:].strip()
                # 查找对应的类型键
                for key, name in AVAILABLE_CODE_TYPES.items():
                    if name == type_name:
                        current_type = type_name
                        current_type_key = key
                        break
                continue
            
            # 如果当前类型在启用的类型列表中，提取番号
            if current_type_key and current_type_key in enabled_types:
                # 提取番号部分（去除日期等信息）
                # 格式如：CAWD-808 (2025-03-28) [单体作品]
                code_match = re.match(r'^([A-Z0-9-]+)', line)
                if code_match:
                    codes.add(code_match.group(1))
    
    return codes

def read_codes_from_file(file_path):
    """读取番号列表文件，返回一个集合（保留兼容性）"""
    codes = set()
    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            # 跳过空行
            if not line:
                continue
            # 跳过分类标题行（以#开头的行）
            if line.startswith('#'):
                continue
            # 提取番号部分（去除日期等信息）
            # 格式如：CAWD-808 (2025-03-28)
            code_match = re.match(r'^([A-Z0-9-]+)', line)
            if code_match:
                codes.add(code_match.group(1))
    return codes

def normalize_filename(filename):
    """规范化文件名，移植自Java的JavRename类"""
    # 定义要去除的前缀和后缀
    starts = ["._", "_www.av9.cc-", "第一會所新片@SIS001@", "boy999.co-", "big-cup.tv-", 
              "4k2.com@", "hhd800.com@", "hhd800com@", "bbs2048.org@", "fbzip.com@", 
              "[fbfb.me]", "fbfb.me@", "icao.me@", "test-", "-2x-RIFE", "HD_", "HD-", 
              "0903-", "freedlorg@", "@bo99.tv_", "[98t.tv]", "@蜜鸟@FENGNIAO151.VIP-", 
              "@蜜鸟@FENGNIAO131.VIP-"]
    
    ends = ["-C", ".", "-", "pl", "-C_2", "_CH", "ch", "hhb", "FHD", "1080P", ".1080p", 
            "_2K", "_4k", "-HD", ".HD", "_full-hd", "-2x-RIFE", "@18P2P", "bbwow", 
            "-icao.me", "_X1080X", "_180_sbs", "~nyap2p.com", "-nyap2p.com", 
            "-carib-high_1", "_scale_4x_alq-113", "_scale_4x_prob-4", 
            "_e9n2646zrk156zlz1", "_ald4tr4ao91znhfh8", "_e9ohw1ox70tmkzlz1", 
            "_e9ipmx2fjr3ewzlz1", "_e91vnlj8rrmbczlz1", "_ale51liakc7nihfh8"]
    
    # 获取文件名（不含扩展名）
    base_name = os.path.splitext(filename)[0]
    extension = os.path.splitext(filename)[1]
    
    new_name = base_name
    
    # 去除无用头部
    for start in starts:
        if new_name.startswith(start):
            new_name = new_name[len(start):]
    
    # 去除无用尾部
    for end in ends:
        if new_name.endswith(end):
            new_name = new_name[:-len(end)]
    
    # 过长表示包含了全名，不进行后续处理
    if len(new_name) > 15:
        return new_name.upper() + extension
    
    # 如果是类似"SSIS001"这种纯字母+数字，在字母和数字之间添加"-"
    if re.match(r'^[A-Za-z0-9]+$', new_name):
        # 在字母序列后的第一个数字前添加"-"
        new_name = re.sub(r'([A-Za-z]{2,6})(\d)', r'\1-\2', new_name)
    
    # 在末尾代表分集序号的数字前修改添加"-"
    new_name = replace_second_to_last_character(new_name)
    
    # 将".part"或"-pt"替换为"-"
    new_name = new_name.replace(".part", "-").replace("-pt", "-")
    
    # 将"00"替换为 "-"（处理如"ipvr00143"这种情况）
    new_name = replace_zeros_between_alphanumeric(new_name)
    
    # 转大写并去除空格
    new_name = new_name.upper().replace(" ", "")
    
    return new_name + extension

def replace_second_to_last_character(input_str):
    """在末尾代表分集序号的数字前修改添加'-'"""
    if len(input_str) < 2:
        return input_str
    
    last_char = input_str[-1]
    second_last_char = input_str[-2]
    
    if last_char.isdigit() and second_last_char in [' ', '-', '_']:
        return input_str[:-2] + '-' + last_char
    
    return input_str

def replace_zeros_between_alphanumeric(input_str):
    """将字母和数字之间的'00'替换为'-'"""
    # 解决kavr-001这种会被转为kavr-1的问题
    if '-00' in input_str:
        return input_str
    
    # 将字母序列后的00替换为-
    result = re.sub(r'([A-Za-z-]+)00([0-9]+)', r'\1-\2', input_str)
    
    # 处理可能产生的双横线
    result = result.replace('--', '-')
    
    return result

def transform_file_list(input_file, output_file):
    """读取文件列表，规范化文件名后写入新文件"""
    transformed_lines = []
    
    with open(input_file, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if not line:
                transformed_lines.append('')
                continue
            
            # 跳过注释行
            if line.startswith('#'):
                transformed_lines.append(line)
                continue
            
            # 获取文件路径的各个部分
            dir_path = os.path.dirname(line)
            filename = os.path.basename(line)
            
            # 规范化文件名
            normalized_filename = normalize_filename(filename)
            
            # 重新组合路径
            new_path = os.path.join(dir_path, normalized_filename)
            transformed_lines.append(new_path)
    
    # 写入转换后的文件
    with open(output_file, 'w', encoding='utf-8') as f:
        for line in transformed_lines:
            f.write(line + '\n')
    
    print(f"已将规范化后的文件列表保存到: {output_file}")
    return output_file

def extract_codes_from_file_list(file_path):
    """从文件列表中提取所有番号，返回一个集合"""
    codes_in_files = set()
    
    # 编译正则表达式以匹配番号
    # 匹配格式如：CAWD-123, KAWD-123等
    code_pattern = re.compile(r'([A-Z]+-\d+)')
    
    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            matches = code_pattern.findall(line)
            for match in matches:
                codes_in_files.add(match)
    
    return codes_in_files

def find_missing_codes(all_codes, existing_codes):
    """找出缺失的番号"""
    return all_codes - existing_codes

def check_empty_directories():
    """检查配置的目录是否为空，返回空目录对应的番号类型"""
    empty_directory_types = set()

    # 检查普通目录
    if not NORMAL_DIRECTORY or not NORMAL_DIRECTORY.strip():
        print(f"⚠️  NORMAL_DIRECTORY 配置为空，将所有普通作品视为缺失")
        empty_directory_types.add("normal")
    elif not os.path.exists(NORMAL_DIRECTORY):
        print(f"⚠️  NORMAL_DIRECTORY 路径不存在: {NORMAL_DIRECTORY}，将所有普通作品视为缺失")
        empty_directory_types.add("normal")
    elif not os.path.isdir(NORMAL_DIRECTORY):
        print(f"⚠️  NORMAL_DIRECTORY 不是有效目录: {NORMAL_DIRECTORY}，将所有普通作品视为缺失")
        empty_directory_types.add("normal")
    else:
        # 检查目录是否为空（没有任何文件）
        has_files = False
        for root, dirs, files in os.walk(NORMAL_DIRECTORY):
            if files:
                has_files = True
                break
        if not has_files:
            print(f"⚠️  NORMAL_DIRECTORY 目录为空: {NORMAL_DIRECTORY}，将所有普通作品视为缺失")
            empty_directory_types.add("normal")

    # 检查VR目录（如果启用VR）
    if INCLUDE_VR:
        if not VR_DIRECTORY or not VR_DIRECTORY.strip():
            print(f"⚠️  VR_DIRECTORY 配置为空，将所有VR作品视为缺失")
            empty_directory_types.add("vr")
        elif not os.path.exists(VR_DIRECTORY):
            print(f"⚠️  VR_DIRECTORY 路径不存在: {VR_DIRECTORY}，将所有VR作品视为缺失")
            empty_directory_types.add("vr")
        elif not os.path.isdir(VR_DIRECTORY):
            print(f"⚠️  VR_DIRECTORY 不是有效目录: {VR_DIRECTORY}，将所有VR作品视为缺失")
            empty_directory_types.add("vr")
        else:
            # 检查目录是否为空（没有任何文件）
            has_files = False
            for root, dirs, files in os.walk(VR_DIRECTORY):
                if files:
                    has_files = True
                    break
            if not has_files:
                print(f"⚠️  VR_DIRECTORY 目录为空: {VR_DIRECTORY}，将所有VR作品视为缺失")
                empty_directory_types.add("vr")

    return empty_directory_types

def find_extra_codes(all_codes, existing_codes):
    """找出额外的番号（在文件夹中存在但不在番号列表中）"""
    return existing_codes - all_codes

def save_missing_codes_to_file(missing_codes, output_file):
    """将缺失的番号保存到文本文件中"""
    with open(output_file, 'w', encoding='utf-8') as f:
        for code in sorted(missing_codes):
            f.write(f"{code}\n")
    print(f"已将 {len(missing_codes)} 个缺失的番号保存到文件：{output_file}")

def list_all_files():
    """生成统一文件列表，将所有类型的文件列到统一文件中"""
    # 获取文件路径配置
    file_paths = get_file_paths()
    unified_file_list = str(file_paths["unified_file_list"])
    
    all_files = []
    
    # 处理普通目录
    print(f"开始处理普通目录: {NORMAL_DIRECTORY}")
    if not os.path.isdir(NORMAL_DIRECTORY):
        print(f"错误: {NORMAL_DIRECTORY} 不是一个有效的目录")
        return False
    
    # 收集普通目录的文件
    normal_files = []
    for root, dirs, files in os.walk(NORMAL_DIRECTORY):
        for file in files:
            file_path = os.path.join(root, file)
            normal_files.append(file_path)
    
    print(f"普通目录中发现 {len(normal_files)} 个文件")
    all_files.extend(normal_files)
    
    # 如果启用VR，处理VR目录
    vr_files = []
    if INCLUDE_VR:
        print(f"\n开始处理VR目录: {VR_DIRECTORY}")
        if not os.path.isdir(VR_DIRECTORY):
            print(f"错误: {VR_DIRECTORY} 不是一个有效的目录")
            return False
        
        # 收集VR目录的文件
        for root, dirs, files in os.walk(VR_DIRECTORY):
            for file in files:
                file_path = os.path.join(root, file)
                vr_files.append(file_path)
        
        print(f"VR目录中发现 {len(vr_files)} 个文件")
        all_files.extend(vr_files)
    
    # 将所有文件写入统一文件
    with open(unified_file_list, 'w', encoding='utf-8') as f:
        # 写入普通文件（添加标识）
        if normal_files:
            f.write("# 普通文件\n")
            for file_path in normal_files:
                f.write(f"{file_path}\n")
            f.write("\n")
        
        # 写入VR文件（添加标识）
        if vr_files:
            f.write("# VR文件\n")
            for file_path in vr_files:
                f.write(f"{file_path}\n")
            f.write("\n")
    
    print(f"\n所有文件列表已保存到统一文件: {unified_file_list}")
    print(f"总计 {len(all_files)} 个文件 (普通: {len(normal_files)}, VR: {len(vr_files)})")
    print("\n文件列表生成完成")
    return True

def main(args=None):
    print("✨ AV文件管理工具 - 统一文件检查工具")
    print("=" * 50)
    
    # 从配置获取文件路径
    file_paths = get_file_paths()
    
    unified_codes_file = file_paths["unified_codes_file"]
    unified_file_list = file_paths["unified_file_list"]
    unified_missing_codes_file = file_paths["unified_missing_codes_file"]
    
    # 定义转换后的文件列表路径
    transformed_file_list = str(Path(unified_file_list).parent / "all_files_trans.txt")
    
    # 如果只是生成文件列表，直接返回
    if args and args.list_only:
        print("📁 只生成文件列表模式")
        success = list_all_files()
        if success:
            print("\n✅ 文件列表生成完成！")
        else:
            print("\n❌ 文件列表生成失败！")
        return
    
    # 确保统一番号文件存在
    if not os.path.exists(unified_codes_file):
        print(f"错误：统一番号文件 {unified_codes_file} 不存在")
        print("请先运行 get_codes.py 生成统一番号文件")
        return
    
    # 检查是否跳过更新文件列表
    skip_update = args and args.skip_update
    
    if not skip_update:
        print(f"\n🔄 更新统一文件列表...")
        if not list_all_files():
            print("生成文件列表失败，程序退出")
            return
        print("✅ 文件列表生成成功\n")
    else:
        if not os.path.exists(unified_file_list):
            print(f"\n⚠️  统一文件列表 {unified_file_list} 不存在，需要先生成")
            print("请移除 --skip-update 参数重新运行")
            return
        print(f"\n📁 跳过更新，使用现有文件列表: {unified_file_list}")
    
    # 规范化文件名并生成转换后的文件列表
    print(f"\n🔄 正在规范化文件名...")
    transform_file_list(unified_file_list, transformed_file_list)
    print(f"✅ 规范化完成，已保存到: {transformed_file_list}\n")
    
    # 从统一文件中读取配置启用的所有类型番号
    all_codes = read_codes_from_unified_file(unified_codes_file, ENABLED_CODE_TYPES)
    print(f"从统一文件读取到 {len(all_codes)} 个番号（包含启用的类型：{', '.join([AVAILABLE_CODE_TYPES[t] for t in ENABLED_CODE_TYPES])}）")
    
    # 分别读取不同类型的番号用于详细统计
    normal_codes = read_codes_from_unified_file(unified_codes_file, ["normal"])
    vr_codes = read_codes_from_unified_file(unified_codes_file, ["vr"])
    collaborative_codes = read_codes_from_unified_file(unified_codes_file, ["collaborative"])
    collection_codes = read_codes_from_unified_file(unified_codes_file, ["collection"])
    
    print(f"  - 普通作品: {len(normal_codes)} 个")
    print(f"  - VR作品: {len(vr_codes)} 个")
    print(f"  - 共演作品: {len(collaborative_codes)} 个")
    print(f"  - 合集作品: {len(collection_codes)} 个")
    
    # 检查空目录情况
    print(f"\n🔍 检查目录配置...")
    empty_directory_types = check_empty_directories()

    # 从转换后的文件列表中提取番号（使用规范化后的文件名）
    all_existing_codes = extract_codes_from_file_list(transformed_file_list)
    print(f"从规范化后的文件列表中发现 {len(all_existing_codes)} 个番号")

    # 找出所有启用类型中缺失的番号
    missing_codes = find_missing_codes(all_codes, all_existing_codes)
    
    # 分别计算各类型的缺失番号用于详细统计
    # 对于空目录的类型，将所有该类型的番号都视为缺失
    if "normal" in ENABLED_CODE_TYPES:
        if "normal" in empty_directory_types:
            missing_normal = normal_codes  # 目录为空，所有番号都缺失
        else:
            missing_normal = find_missing_codes(normal_codes, all_existing_codes)
    else:
        missing_normal = set()

    if "vr" in ENABLED_CODE_TYPES:
        if "vr" in empty_directory_types:
            missing_vr = vr_codes  # 目录为空，所有番号都缺失
        else:
            missing_vr = find_missing_codes(vr_codes, all_existing_codes)
    else:
        missing_vr = set()

    if "collaborative" in ENABLED_CODE_TYPES:
        # 共演作品不受目录配置影响，因为它们可能存在于普通目录中
        missing_collaborative = find_missing_codes(collaborative_codes, all_existing_codes)
    else:
        missing_collaborative = set()

    if "collection" in ENABLED_CODE_TYPES:
        # 合集作品不受目录配置影响，因为它们可能存在于普通目录中
        missing_collection = find_missing_codes(collection_codes, all_existing_codes)
    else:
        missing_collection = set()

    # 重新计算总的缺失番号（包含空目录的影响）
    missing_codes = missing_normal | missing_vr | missing_collaborative | missing_collection
    
    if missing_codes:
        print(f"\n发现 {len(missing_codes)} 个缺失的番号")
        print(f"  - 普通作品: {len(missing_normal)} 个")
        print(f"  - VR作品: {len(missing_vr)} 个")
        print(f"  - 共演作品: {len(missing_collaborative)} 个")
        print(f"  - 合集作品: {len(missing_collection)} 个")
        
        # 保存所有缺失的番号到统一文件
        with open(unified_missing_codes_file, 'w', encoding='utf-8') as f:
            if missing_normal:
                f.write("# 普通作品\n")
                for code in sorted(missing_normal):
                    f.write(f"{code}\n")
                f.write("\n")
            
            if missing_vr:
                f.write("# VR作品\n")
                for code in sorted(missing_vr):
                    f.write(f"{code}\n")
                f.write("\n")
            
            if missing_collaborative:
                f.write("# 共演作品\n")
                for code in sorted(missing_collaborative):
                    f.write(f"{code}\n")
                f.write("\n")
            
            if missing_collection:
                f.write("# 合集作品\n")
                for code in sorted(missing_collection):
                    f.write(f"{code}\n")
                f.write("\n")
        
        print(f"已将所有缺失番号保存到统一文件: {unified_missing_codes_file}")
        
        # 打印缺失的番号
        print("\n缺失的番号列表：")
        for code in sorted(missing_codes):
            print(code)
    else:
        print("\n文件夹中包含所有列表中的番号")
        # 创建空的统一文件
        with open(unified_missing_codes_file, 'w', encoding='utf-8') as f:
            pass
    
    # 找出额外的番号
    extra_codes = find_extra_codes(all_codes, all_existing_codes)
    
    if extra_codes:
        print(f"\n文件夹中存在以下 {len(extra_codes)} 个额外的番号（不在番号列表中）：")
        for code in sorted(extra_codes):
            print(code)
    else:
        print("\n文件夹中没有额外的番号")

if __name__ == "__main__":
    # 设置命令行参数
    parser = argparse.ArgumentParser(
        description="AV文件管理工具 - 统一文件检查工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python check_missing_codes.py                    # 更新文件列表并检查缺失番号（默认）
  python check_missing_codes.py --skip-update      # 跳过文件列表更新，直接检查
  python check_missing_codes.py --list-only        # 只生成文件列表
        """
    )
    
    parser.add_argument(
        "--list-only", "-l",
        action="store_true",
        help="只生成统一文件列表，不进行缺失番号检查"
    )
    
    parser.add_argument(
        "--skip-update", "-s",
        action="store_true",
        help="跳过文件列表更新，使用现有的文件列表（默认会更新）"
    )
    
    args = parser.parse_args()
    main(args)
