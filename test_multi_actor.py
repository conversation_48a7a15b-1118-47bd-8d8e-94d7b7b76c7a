#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试多人作品检测功能
"""

import sys
from pathlib import Path

# 添加路径以便导入
sys.path.append(str(Path(__file__).parent / "avmanage"))
sys.path.append(str(Path(__file__).parent / "avmanage" / "getav"))

try:
    from get_codes_requests import RequestsCodeScraper
    from av_config import MULTI_ACTOR_THRESHOLD
    
    def test_multi_actor_logic():
        """测试多人作品分类逻辑"""
        print("🧪 测试多人作品分类逻辑")
        print("=" * 50)
        
        # 创建调试模式的爬虫实例
        scraper = RequestsCodeScraper(debug_mode=True)
        
        print(f"多人作品阈值: {MULTI_ACTOR_THRESHOLD}")
        print()
        
        # 模拟不同演员数量的测试数据
        test_cases = [
            (1, "单体作品"),
            (2, "共演作品"),
            (3, "共演作品"),
            (4, "共演作品"),
            (5, "合集作品"),
            (8, "合集作品"),
            (10, "合集作品")
        ]
        
        print("📊 分类逻辑测试:")
        print("-" * 30)
        
        for actress_count, expected_type in test_cases:
            # 模拟作品信息
            code_text = f"TEST-{actress_count:03d}"
            release_date = "2024-01-01"
            video_type = "测试作品"
            is_vr = False
            
            # 模拟分类逻辑
            if is_vr:
                result_type = "VR作品"
            else:
                if actress_count == 1:
                    result_type = "单体作品"
                elif actress_count > 1:
                    if actress_count <= MULTI_ACTOR_THRESHOLD:
                        result_type = "共演作品"
                    else:
                        result_type = "合集作品"
                else:
                    result_type = "单体作品"
            
            status = "✅" if result_type == expected_type else "❌"
            print(f"  {status} {actress_count}人 -> {result_type} (期望: {expected_type})")
        
        print()
        print("🔍 实际网页测试:")
        print("-" * 30)
        
        # 测试实际的网页爬取（只爬取第一页）
        test_url = "https://av-wiki.net/av-actress/kurata-mao/"
        print(f"测试URL: {test_url}")
        
        page_results, next_url = scraper.scrape_page(test_url)
        
        if page_results:
            print("\n📈 实际爬取结果:")
            total_items = sum(len(codes) for codes in page_results.values())
            print(f"总计处理: {total_items} 个作品")
            
            for code_type, codes_list in page_results.items():
                if codes_list:
                    print(f"  - {code_type}: {len(codes_list)} 个")
                    
                    # 显示前2个作品的详细信息
                    if len(codes_list) > 0:
                        print(f"    示例:")
                        for i, (code, date, video_type) in enumerate(codes_list[:2]):
                            print(f"      {i+1}. {code} ({date}) [{video_type}]")
                        if len(codes_list) > 2:
                            print(f"      ... 还有 {len(codes_list) - 2} 个")
                    print()
            
            # 检查是否有多人作品
            multi_actor_count = len(page_results.get('multi_actor', []))
            collaborative_count = len(page_results.get('collaborative', []))
            collection_count = len(page_results.get('collection', []))
            
            print("🎯 多人作品检测结果:")
            print(f"  - 多人作品总计: {multi_actor_count}")
            print(f"  - 共演作品: {collaborative_count}")
            print(f"  - 合集作品: {collection_count}")
            
            if multi_actor_count > 0:
                print("  ✅ 检测到多人作品！")
            else:
                print("  ⚠️  未检测到多人作品（可能该演员主要是单体作品）")
                
        else:
            print("❌ 网页爬取失败")
            
    if __name__ == "__main__":
        test_multi_actor_logic()
        
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("请确保 avmanage/getav/ 目录下有正确的配置文件")
except Exception as e:
    print(f"❌ 测试过程中发生错误: {e}")
    import traceback
    traceback.print_exc()
