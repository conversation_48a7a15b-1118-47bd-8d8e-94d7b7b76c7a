import requests
import ssl
import socket
import threading
import time
from urllib.parse import urlparse
import hashlib
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed

class SSLHijackDetector:
    def __init__(self, test_urls=None, timeout=10, max_workers=20):
        self.test_urls = test_urls or [
            'https://www.google.com',
            'https://www.github.com',
            'https://www.cloudflare.com'
        ]
        self.timeout = timeout
        self.max_workers = max_workers
        self.reference_certs = {}
        self.results = []
        
    def get_reference_certificates(self):
        """获取不使用代理时的参考证书"""
        print("正在获取参考证书...")
        for url in self.test_urls:
            try:
                parsed = urlparse(url)
                hostname = parsed.hostname
                port = parsed.port or 443
                
                context = ssl.create_default_context()
                with socket.create_connection((hostname, port), timeout=self.timeout) as sock:
                    with context.wrap_socket(sock, server_hostname=hostname) as ssock:
                        # 使用getpeercert获取二进制格式的证书
                        cert_der = ssock.getpeercert(binary_form=True)
                        cert_hash = hashlib.sha256(cert_der).hexdigest()
                        self.reference_certs[url] = cert_hash
                        print(f"  {url}: {cert_hash[:16]}...")
            except Exception as e:
                print(f"  获取 {url} 参考证书失败: {e}")
                
    def test_proxy_ssl(self, proxy_url):
        """测试单个代理的SSL证书"""
        result = {
            'proxy': proxy_url,
            'status': 'unknown',
            'details': {},
            'hijacked': False,
            'error': None
        }
        
        try:
            # 解析代理URL
            parsed_proxy = urlparse(proxy_url)
            
            # 尝试通过SOCKS代理连接测试网站
            for test_url in self.test_urls:
                try:
                    # 使用socks库进行SOCKS5连接测试
                    import socks
                    
                    parsed_url = urlparse(test_url)
                    hostname = parsed_url.hostname
                    port = parsed_url.port or 443
                    
                    # 创建SOCKS socket
                    sock = socks.socksocket()
                    sock.set_proxy(socks.SOCKS5, parsed_proxy.hostname, parsed_proxy.port)
                    sock.settimeout(self.timeout)
                    
                    # 尝试连接
                    sock.connect((hostname, port))
                    
                    # 建立SSL连接测试
                    context = ssl.create_default_context()
                    context.check_hostname = False
                    context.verify_mode = ssl.CERT_NONE
                    
                    ssl_sock = context.wrap_socket(sock, server_hostname=hostname)
                    ssl_sock.close()
                    
                    result['details'][test_url] = {
                        'accessible': True,
                        'ssl_working': True
                    }
                        
                except Exception as e:
                    result['details'][test_url] = {
                        'error': str(e),
                        'accessible': False
                    }
            
            # 检查是否有任何测试URL能访问
            accessible_count = sum(1 for details in result['details'].values() 
                                 if details.get('accessible', False))
            
            if accessible_count > 0:
                result['status'] = 'working'
            else:
                result['status'] = 'failed'
                
        except Exception as e:
            result['error'] = str(e)
            result['status'] = 'error'
            
        return result
    
    def detect_ssl_hijack_detailed(self, proxy_url):
        """详细检测SSL证书劫持"""
        result = {
            'proxy': proxy_url,
            'hijacked': False,
            'cert_mismatches': [],
            'error': None,
            'working': False
        }
        
        try:
            parsed_proxy = urlparse(proxy_url)
            proxy_host = parsed_proxy.hostname
            proxy_port = parsed_proxy.port
            
            for test_url in self.test_urls:
                if test_url not in self.reference_certs:
                    continue
                    
                try:
                    parsed_url = urlparse(test_url)
                    hostname = parsed_url.hostname
                    port = parsed_url.port or 443
                    
                    # 通过SOCKS代理建立连接
                    import socks
                    sock = socks.socksocket()
                    sock.set_proxy(socks.SOCKS5, proxy_host, proxy_port)
                    sock.settimeout(self.timeout)
                    sock.connect((hostname, port))
                    
                    # 建立SSL连接
                    context = ssl.create_default_context()
                    context.check_hostname = False
                    context.verify_mode = ssl.CERT_NONE
                    
                    ssl_sock = context.wrap_socket(sock, server_hostname=hostname)
                    # 使用getpeercert获取二进制格式的证书
                    cert_der = ssl_sock.getpeercert(binary_form=True)
                    cert_hash = hashlib.sha256(cert_der).hexdigest()
                    
                    ssl_sock.close()
                    
                    # 比较证书哈希
                    if cert_hash != self.reference_certs[test_url]:
                        result['hijacked'] = True
                        result['cert_mismatches'].append({
                            'url': test_url,
                            'expected': self.reference_certs[test_url],
                            'actual': cert_hash
                        })
                    
                    result['working'] = True
                    
                except Exception as e:
                    print(f"    检测 {test_url} 时出错: {e}")
                    
        except Exception as e:
            result['error'] = str(e)
            
        return result
    
    def load_proxies(self, filename):
        """从文件加载代理列表"""
        proxies = []
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and line.startswith('socks5://'):
                        proxies.append(line)
            print(f"加载了 {len(proxies)} 个代理")
        except FileNotFoundError:
            print(f"文件 {filename} 不存在")
        except Exception as e:
            print(f"加载代理文件时出错: {e}")
        return proxies
    
    def run_detection(self, proxy_file='socks5.txt'):
        """运行批量检测 - 两阶段检测模式"""
        print("=== SOCKS5代理SSL证书劫持检测工具 ===\n")
        
        # 加载代理列表
        proxies = self.load_proxies(proxy_file)
        if not proxies:
            print("没有找到可用的代理")
            return
        
        return self._run_two_stage_detection(proxies)

    

    
    def _run_two_stage_detection(self, proxies):
        """运行两阶段检测模式：先快速检查，再详细检查"""
        
        # 第一阶段：快速检测
        print(f"\n📋 第一阶段：快速检测 {len(proxies)} 个代理的基本连通性...")
        print("-" * 60)
        
        working_proxies = []
        basic_results = []
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            future_to_proxy = {
                executor.submit(self.test_proxy_ssl, proxy): proxy 
                for proxy in proxies
            }
            
            for future in as_completed(future_to_proxy):
                proxy = future_to_proxy[future]
                try:
                    result = future.result()
                    basic_results.append(result)
                    
                    if result['status'] == 'working':
                        working_proxies.append(proxy)
                        print(f"🟢 连通性测试通过 - {proxy}")
                    else:
                        print(f"🔴 连通性测试失败 - {proxy}")
                        if result.get('error'):
                            print(f"  错误: {result['error']}")
                        
                except Exception as e:
                    print(f"🔴 检测出错 - {proxy}: {e}")
        
        print(f"\n第一阶段完成: {len(working_proxies)}/{len(proxies)} 个代理通过基本连通性测试")
        
        if not working_proxies:
            print("❌ 没有可用的代理，跳过详细检测")
            return basic_results
        
        # 第二阶段：详细SSL证书检测
        print(f"\n🔍 第二阶段：对 {len(working_proxies)} 个可用代理进行SSL证书劫持检测...")
        print("-" * 60)
        
        # 获取参考证书
        self.get_reference_certificates()
        
        if not self.reference_certs:
            print("❌ 无法获取参考证书，跳过详细检测")
            return basic_results
        
        detailed_results = []
        hijacked_count = 0
        ssl_working_count = 0
        
        with ThreadPoolExecutor(max_workers=min(self.max_workers, len(working_proxies))) as executor:
            future_to_proxy = {
                executor.submit(self.detect_ssl_hijack_detailed, proxy): proxy 
                for proxy in working_proxies
            }
            
            for future in as_completed(future_to_proxy):
                proxy = future_to_proxy[future]
                try:
                    result = future.result()
                    detailed_results.append(result)
                    
                    if result['working']:
                        ssl_working_count += 1
                        if result['hijacked']:
                            hijacked_count += 1
                            print(f"🔴 检测到SSL证书劫持! - {proxy}")
                            for mismatch in result['cert_mismatches']:
                                print(f"  ⚠️  {mismatch['url']} 证书不匹配")
                                print(f"      期望: {mismatch['expected'][:32]}...")
                                print(f"      实际: {mismatch['actual'][:32]}...")
                        else:
                            print(f"🟢 SSL证书检测通过 - {proxy}")
                    else:
                        print(f"🔴 SSL连接失败 - {proxy}")
                    
                    if result.get('error'):
                        print(f"  错误: {result['error']}")
                        
                except Exception as e:
                    print(f"🔴 详细检测出错 - {proxy}: {e}")
        
        # 合并结果
        all_results = basic_results + detailed_results
        self.results = all_results
        
        # 输出最终总结
        print("\n" + "=" * 60)
        print("🎯 两阶段检测完成!")
        print(f"总计代理: {len(proxies)}")
        print(f"第一阶段通过 (基本连通): {len(working_proxies)}")
        print(f"第二阶段通过 (SSL工作): {ssl_working_count}")
        print(f"检测到SSL劫持: {hijacked_count}")
        print(f"最终可用代理: {ssl_working_count - hijacked_count}")
        
        # 保存不同类型的代理到文件
        self._save_categorized_proxies(working_proxies, detailed_results, hijacked_count)
        
        return all_results
    
    def _save_categorized_proxies(self, working_proxies, detailed_results, hijacked_count):
        """保存分类后的代理到不同文件"""
        clean_proxies = []
        hijacked_proxies = []
        
        for result in detailed_results:
            if result['working']:
                if result['hijacked']:
                    hijacked_proxies.append(result['proxy'])
                else:
                    clean_proxies.append(result['proxy'])
        
        # 保存干净的代理
        if clean_proxies:
            with open('clean_proxies.txt', 'w', encoding='utf-8') as f:
                for proxy in clean_proxies:
                    f.write(proxy + '\n')
            print(f"✅ 已将 {len(clean_proxies)} 个干净代理保存到 clean_proxies.txt")
        
        # 保存被劫持的代理
        if hijacked_proxies:
            with open('hijacked_proxies.txt', 'w', encoding='utf-8') as f:
                for proxy in hijacked_proxies:
                    f.write(proxy + '\n')
            print(f"⚠️  已将 {len(hijacked_proxies)} 个被劫持代理保存到 hijacked_proxies.txt")
        
        # 保存所有工作正常的代理（包括第一阶段通过但第二阶段未测试的）
        if working_proxies:
            with open('working_proxies.txt', 'w', encoding='utf-8') as f:
                for proxy in working_proxies:
                    f.write(proxy + '\n')
            print(f"📋 已将 {len(working_proxies)} 个基本可用代理保存到 working_proxies.txt")

def main():
    """主函数"""
    # 创建检测器实例
    detector = SSLHijackDetector(
        test_urls=[
            'https://www.google.com',
            'https://www.github.com',
            'https://www.cloudflare.com'
        ],
        timeout=10,
        max_workers=10  # 降低并发数以避免过多连接
    )
    
    # 运行两阶段检测 (设置detailed=True启用两阶段模式)
    print("🚀 启动两阶段检测模式：先快速检查连通性，再详细检查SSL证书")
    results = detector.run_detection('socks5.txt')

if __name__ == "__main__":
    main()

